# Fintech Platform Environment Variables
# Copy this file to .env and update with your actual values

# =============================================================================
# CORE CONFIGURATION
# =============================================================================

# Environment
ENVIRONMENT=development
DEBUG=true

# Server Configuration
SERVER_PORT=8080
SERVER_HOST=0.0.0.0

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# PostgreSQL Database
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=fintech_platform
DATABASE_USER=postgres
DATABASE_PASSWORD=postgres
DATABASE_SSL_MODE=disable
DATABASE_MAX_OPEN_CONNS=25
DATABASE_MAX_IDLE_CONNS=5
DATABASE_CONN_MAX_LIFETIME=300

# =============================================================================
# CACHE CONFIGURATION
# =============================================================================

# Redis Cache
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_POOL_SIZE=10

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRY=24h
REFRESH_TOKEN_EXPIRY=720h

# Webhook Security
WEBHOOK_SECRET=your-webhook-secret-key-change-this

# Encryption Keys
ENCRYPTION_KEY=your-32-character-encryption-key

# =============================================================================
# NOTIFICATION SERVICES
# =============================================================================

# Email Configuration (SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=Fintech Platform

# SMS Configuration (Twilio)
SMS_API_KEY=your-twilio-account-sid
SMS_API_SECRET=your-twilio-auth-token
SMS_FROM_NUMBER=+**********

# Push Notifications (Firebase)
PUSH_API_KEY=your-firebase-server-key
PUSH_PROJECT_ID=your-firebase-project-id

# =============================================================================
# BLOCKCHAIN INTEGRATIONS
# =============================================================================

# Ethereum
ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/your-project-id
ETHEREUM_PRIVATE_KEY=your-ethereum-private-key
ETHEREUM_GAS_LIMIT=21000
ETHEREUM_GAS_PRICE=***********

# Bitcoin
BITCOIN_RPC_URL=https://your-bitcoin-node.com
BITCOIN_RPC_USERNAME=your-bitcoin-rpc-username
BITCOIN_RPC_PASSWORD=your-bitcoin-rpc-password

# Solana
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
SOLANA_PRIVATE_KEY=your-solana-private-key

# Polygon
POLYGON_RPC_URL=https://polygon-mainnet.infura.io/v3/your-project-id
POLYGON_PRIVATE_KEY=your-polygon-private-key

# =============================================================================
# KYC/AML PROVIDERS
# =============================================================================

# Jumio
JUMIO_API_TOKEN=your-jumio-api-token
JUMIO_API_SECRET=your-jumio-api-secret
JUMIO_BASE_URL=https://netverify.com

# Onfido
ONFIDO_API_TOKEN=your-onfido-api-token
ONFIDO_BASE_URL=https://api.onfido.com

# Sumsub
SUMSUB_APP_TOKEN=your-sumsub-app-token
SUMSUB_SECRET_KEY=your-sumsub-secret-key
SUMSUB_BASE_URL=https://api.sumsub.com

# =============================================================================
# PAYMENT PROCESSORS
# =============================================================================

# Stripe
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret

# Circle (USDC)
CIRCLE_API_KEY=your-circle-api-key
CIRCLE_BASE_URL=https://api.circle.com

# PayPal
PAYPAL_CLIENT_ID=your-paypal-client-id
PAYPAL_CLIENT_SECRET=your-paypal-client-secret
PAYPAL_BASE_URL=https://api.sandbox.paypal.com

# =============================================================================
# CARD ISSUERS
# =============================================================================

# Marqeta
MARQETA_USERNAME=your-marqeta-username
MARQETA_PASSWORD=your-marqeta-password
MARQETA_BASE_URL=https://sandbox-api.marqeta.com

# Galileo
GALILEO_API_LOGIN=your-galileo-api-login
GALILEO_API_PASSWORD=your-galileo-api-password
GALILEO_BASE_URL=https://api.galileo-ft.com

# =============================================================================
# MARKET DATA PROVIDERS
# =============================================================================

# CoinMarketCap
CMC_API_KEY=your-coinmarketcap-api-key
CMC_BASE_URL=https://pro-api.coinmarketcap.com

# CoinGecko
COINGECKO_API_KEY=your-coingecko-api-key
COINGECKO_BASE_URL=https://api.coingecko.com

# Alpha Vantage
ALPHA_VANTAGE_API_KEY=your-alpha-vantage-api-key
ALPHA_VANTAGE_BASE_URL=https://www.alphavantage.co

# =============================================================================
# EXCHANGE INTEGRATIONS
# =============================================================================

# Binance
BINANCE_API_KEY=your-binance-api-key
BINANCE_SECRET_KEY=your-binance-secret-key
BINANCE_BASE_URL=https://api.binance.com

# Coinbase Pro
COINBASE_API_KEY=your-coinbase-api-key
COINBASE_SECRET_KEY=your-coinbase-secret-key
COINBASE_PASSPHRASE=your-coinbase-passphrase
COINBASE_BASE_URL=https://api.pro.coinbase.com

# Kraken
KRAKEN_API_KEY=your-kraken-api-key
KRAKEN_SECRET_KEY=your-kraken-secret-key
KRAKEN_BASE_URL=https://api.kraken.com

# =============================================================================
# DEFI PROTOCOL INTEGRATIONS
# =============================================================================

# Uniswap
UNISWAP_ROUTER_ADDRESS=0x7a250d5630B4cF539739dF2C5dAcb4c659F2488D
UNISWAP_FACTORY_ADDRESS=******************************************

# Compound
COMPOUND_COMPTROLLER_ADDRESS=******************************************

# Aave
AAVE_LENDING_POOL_ADDRESS=******************************************

# Curve
CURVE_REGISTRY_ADDRESS=******************************************

# =============================================================================
# MONITORING & OBSERVABILITY
# =============================================================================

# Prometheus
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=9090

# Jaeger Tracing
JAEGER_ENABLED=false
JAEGER_ENDPOINT=http://localhost:14268/api/traces

# Sentry Error Tracking
SENTRY_DSN=your-sentry-dsn
SENTRY_ENVIRONMENT=development

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Module Enablement
ACCOUNTS_MODULE_ENABLED=true
PAYMENTS_MODULE_ENABLED=true
YIELD_MODULE_ENABLED=true
TRADING_MODULE_ENABLED=true
CARDS_MODULE_ENABLED=true

# Feature Flags
EXPERIMENTAL_FEATURES=false
BETA_FEATURES=true
ADVANCED_TRADING=true
INSTITUTIONAL_FEATURES=false
MOBILE_APP_SUPPORT=true
WEB_APP_SUPPORT=true
API_ACCESS=true
WEBHOOK_SUPPORT=true
REAL_TIME_NOTIFICATIONS=true
MULTI_CURRENCY_SUPPORT=true
CROSS_BORDER_PAYMENTS=true
REGULATORY_REPORTING=true

# =============================================================================
# RATE LIMITING
# =============================================================================

# API Rate Limits
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST_SIZE=20

# =============================================================================
# COMPLIANCE & REGULATORY
# =============================================================================

# AML/KYC Settings
AML_ENABLED=true
SANCTIONS_CHECK_ENABLED=true
PEP_CHECK_ENABLED=true
RISK_SCORING_ENABLED=true
TRANSACTION_LIMITS_ENABLED=true
REPORTING_REQUIRED=true

# Allowed/Blocked Countries (ISO 3166-1 alpha-3)
ALLOWED_COUNTRIES=USA,CAN,GBR,DEU,FRA,AUS,JPN
BLOCKED_COUNTRIES=IRN,PRK,SYR

# =============================================================================
# BACKUP & DISASTER RECOVERY
# =============================================================================

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_TYPE=s3

# AWS S3 (for backups)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-backup-bucket

# =============================================================================
# DEVELOPMENT & TESTING
# =============================================================================

# Development Settings
LOG_LEVEL=info
LOG_FORMAT=json
ENABLE_CORS=true
ENABLE_SWAGGER=true

# Testing
TEST_DATABASE_NAME=fintech_platform_test
ENABLE_TEST_ENDPOINTS=false

# =============================================================================
# PERFORMANCE TUNING
# =============================================================================

# Connection Pools
MAX_IDLE_CONNS=10
MAX_OPEN_CONNS=100
CONN_MAX_LIFETIME=3600

# Timeouts
READ_TIMEOUT=30
WRITE_TIMEOUT=30
IDLE_TIMEOUT=120

# Cache TTL
CACHE_TTL_SECONDS=3600
SESSION_TTL_SECONDS=86400

# =============================================================================
# NOTES
# =============================================================================

# 1. Never commit this file with real credentials to version control
# 2. Use strong, unique passwords and API keys
# 3. Rotate credentials regularly
# 4. Use environment-specific values for different deployments
# 5. Consider using a secrets management system for production
# 6. Validate all external API credentials before deployment
# 7. Monitor API usage and rate limits
# 8. Keep this file updated with new configuration options
