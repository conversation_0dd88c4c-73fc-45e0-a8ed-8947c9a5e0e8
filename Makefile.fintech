# Fintech Platform Makefile
# This Makefile provides convenient commands for managing the fintech platform

.PHONY: help build start stop restart logs clean test lint format deps check-env setup-env

# Default target
help: ## Show this help message
	@echo "Fintech Platform - Available Commands:"
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

# Environment setup
check-env: ## Check if required environment variables are set
	@echo "Checking environment variables..."
	@test -n "$(JWT_SECRET)" || (echo "❌ JWT_SECRET is not set" && exit 1)
	@echo "✅ All required environment variables are set"

setup-env: ## Create .env file from template
	@if [ ! -f .env ]; then \
		cp .env.fintech.example .env; \
		echo "📝 Created .env file from template"; \
		echo "Please edit .env file with your API keys and configuration"; \
	else \
		echo "⚠️  .env file already exists"; \
	fi

# Build commands
build: ## Build all Docker images
	@echo "🔨 Building Docker images..."
	docker-compose -f docker-compose.fintech.yml build

build-api: ## Build only the fintech API
	@echo "🔨 Building fintech API..."
	docker-compose -f docker-compose.fintech.yml build fintech-api

build-content-analysis: ## Build only the content analysis service
	@echo "🔨 Building content analysis service..."
	docker-compose -f docker-compose.fintech.yml build content-analysis-service

# Start/Stop commands
start: check-env ## Start all services
	@echo "🚀 Starting Fintech Platform..."
	docker-compose -f docker-compose.fintech.yml up -d
	@echo "✅ Platform started successfully"
	@echo ""
	@echo "📊 Access points:"
	@echo "  - Fintech API: http://localhost:8080"
	@echo "  - API Documentation: http://localhost:8081"
	@echo "  - Grafana Dashboard: http://localhost:3000 (admin/admin)"
	@echo "  - Prometheus: http://localhost:9091"
	@echo "  - Database Admin: http://localhost:5050 (<EMAIL>/admin)"
	@echo "  - Redis Admin: http://localhost:8001"

start-dev: ## Start services in development mode (with logs)
	@echo "🚀 Starting Fintech Platform in development mode..."
	docker-compose -f docker-compose.fintech.yml up

stop: ## Stop all services
	@echo "🛑 Stopping Fintech Platform..."
	docker-compose -f docker-compose.fintech.yml down

restart: stop start ## Restart all services

# Infrastructure commands
start-infra: ## Start only infrastructure services
	@echo "🏗️  Starting infrastructure services..."
	docker-compose -f docker-compose.fintech.yml up -d postgres redis prometheus grafana

stop-infra: ## Stop only infrastructure services
	@echo "🛑 Stopping infrastructure services..."
	docker-compose -f docker-compose.fintech.yml stop postgres redis prometheus grafana

# Logs commands
logs: ## Show logs from all services
	docker-compose -f docker-compose.fintech.yml logs

logs-follow: ## Follow logs from all services
	docker-compose -f docker-compose.fintech.yml logs -f

logs-api: ## Show logs from fintech API
	docker-compose -f docker-compose.fintech.yml logs fintech-api

logs-db: ## Show logs from database
	docker-compose -f docker-compose.fintech.yml logs postgres

logs-redis: ## Show logs from Redis
	docker-compose -f docker-compose.fintech.yml logs redis

# Status commands
status: ## Show status of all services
	@echo "📊 Service Status:"
	@docker-compose -f docker-compose.fintech.yml ps

health: ## Check health of all services
	@echo "🏥 Health Check:"
	@echo "Fintech API:"
	@curl -s http://localhost:8080/health | jq . || echo "❌ API not responding"
	@echo ""
	@echo "Grafana:"
	@curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 && echo "✅ Grafana is running" || echo "❌ Grafana not responding"
	@echo ""
	@echo "Prometheus:"
	@curl -s -o /dev/null -w "%{http_code}" http://localhost:9091 && echo "✅ Prometheus is running" || echo "❌ Prometheus not responding"

# Database commands
db-init: ## Initialize database schema
	@echo "🗄️  Initializing database..."
	docker-compose -f docker-compose.fintech.yml exec postgres psql -U postgres -d fintech_platform -f /docker-entrypoint-initdb.d/01-schema.sql

db-reset: ## Reset database (WARNING: This will delete all data)
	@echo "⚠️  This will delete all data. Are you sure? [y/N]" && read ans && [ $${ans:-N} = y ]
	@echo "🗄️  Resetting database..."
	docker-compose -f docker-compose.fintech.yml exec postgres psql -U postgres -c "DROP DATABASE IF EXISTS fintech_platform;"
	docker-compose -f docker-compose.fintech.yml exec postgres psql -U postgres -c "CREATE DATABASE fintech_platform;"
	$(MAKE) db-init

db-backup: ## Backup database
	@echo "💾 Creating database backup..."
	@mkdir -p backups
	docker-compose -f docker-compose.fintech.yml exec postgres pg_dump -U postgres fintech_platform > backups/fintech_platform_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "✅ Backup created in backups/ directory"

db-shell: ## Open database shell
	docker-compose -f docker-compose.fintech.yml exec postgres psql -U postgres -d fintech_platform

# Development commands
dev-setup: setup-env deps build start-infra ## Complete development setup
	@echo "🎯 Development environment setup complete!"
	@echo "Run 'make start' to start all services"

deps: ## Install Go dependencies
	@echo "📦 Installing Go dependencies..."
	go mod download
	go mod tidy

test: ## Run tests
	@echo "🧪 Running tests..."
	go test -v ./...

test-coverage: ## Run tests with coverage
	@echo "🧪 Running tests with coverage..."
	go test -v -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html
	@echo "📊 Coverage report generated: coverage.html"

lint: ## Run linter
	@echo "🔍 Running linter..."
	golangci-lint run

format: ## Format code
	@echo "🎨 Formatting code..."
	go fmt ./...
	goimports -w .

# API testing commands
test-api: ## Test API endpoints
	@echo "🧪 Testing API endpoints..."
	@echo "Health check:"
	@curl -s http://localhost:8080/health | jq .
	@echo ""
	@echo "API version:"
	@curl -s http://localhost:8080/api/v1/health | jq .

test-accounts: ## Test accounts endpoints
	@echo "🧪 Testing accounts endpoints..."
	@echo "Creating test account:"
	@curl -X POST http://localhost:8080/api/v1/accounts/register \
		-H "Content-Type: application/json" \
		-d '{"email":"<EMAIL>","phone":"+**********","first_name":"Test","last_name":"User","password":"password123","account_type":"personal","country":"USA","accept_terms":true}' | jq .

test-payments: ## Test payments endpoints
	@echo "🧪 Testing payments endpoints..."
	@echo "This would test payment creation and processing"

test-yield: ## Test yield endpoints
	@echo "🧪 Testing yield endpoints..."
	@echo "This would test yield position creation"

test-trading: ## Test trading endpoints
	@echo "🧪 Testing trading endpoints..."
	@echo "This would test order creation"

test-cards: ## Test cards endpoints
	@echo "🧪 Testing cards endpoints..."
	@echo "This would test card creation"

# Monitoring commands
monitor: ## Open monitoring dashboard
	@echo "📊 Opening monitoring dashboard..."
	@open http://localhost:3000 || xdg-open http://localhost:3000 || echo "Please open http://localhost:3000 in your browser"

docs: ## Open API documentation
	@echo "📚 Opening API documentation..."
	@open http://localhost:8081 || xdg-open http://localhost:8081 || echo "Please open http://localhost:8081 in your browser"

# Cleanup commands
clean: ## Clean up Docker resources
	@echo "🧹 Cleaning up Docker resources..."
	docker-compose -f docker-compose.fintech.yml down -v
	docker system prune -f

clean-all: ## Clean up everything (images, volumes, networks)
	@echo "🧹 Cleaning up all Docker resources..."
	docker-compose -f docker-compose.fintech.yml down -v --rmi all
	docker system prune -a -f

# Data management commands
export-data: ## Export data from all modules
	@echo "📤 Exporting data..."
	@mkdir -p exports
	docker-compose -f docker-compose.fintech.yml exec postgres psql -U postgres -d fintech_platform -c "\copy accounts.accounts TO '/tmp/accounts.csv' CSV HEADER;"
	docker-compose -f docker-compose.fintech.yml exec postgres psql -U postgres -d fintech_platform -c "\copy payments.payments TO '/tmp/payments.csv' CSV HEADER;"
	docker-compose -f docker-compose.fintech.yml exec postgres psql -U postgres -d fintech_platform -c "\copy yield_farming.positions TO '/tmp/yield_positions.csv' CSV HEADER;"
	docker-compose -f docker-compose.fintech.yml exec postgres psql -U postgres -d fintech_platform -c "\copy trading.orders TO '/tmp/orders.csv' CSV HEADER;"
	docker-compose -f docker-compose.fintech.yml exec postgres psql -U postgres -d fintech_platform -c "\copy cards.cards TO '/tmp/cards.csv' CSV HEADER;"
	docker cp fintech-postgres:/tmp/accounts.csv exports/
	docker cp fintech-postgres:/tmp/payments.csv exports/
	docker cp fintech-postgres:/tmp/yield_positions.csv exports/
	docker cp fintech-postgres:/tmp/orders.csv exports/
	docker cp fintech-postgres:/tmp/cards.csv exports/
	@echo "✅ Data exported to exports/ directory"

import-sample-data: ## Import sample data for testing
	@echo "📥 Importing sample data..."
	docker-compose -f docker-compose.fintech.yml exec postgres psql -U postgres -d fintech_platform -f /docker-entrypoint-initdb.d/02-seed.sql

# Performance commands
benchmark: ## Run performance benchmarks
	@echo "⚡ Running performance benchmarks..."
	@echo "This would run performance tests against the API"

load-test: ## Run load tests
	@echo "🔥 Running load tests..."
	@echo "This would run load tests against the system"

# Security commands
security-scan: ## Run security scan
	@echo "🔒 Running security scan..."
	@echo "This would run security scans on the codebase"

# Module-specific commands
accounts-only: ## Start only accounts module
	@echo "👤 Starting accounts module..."
	docker-compose -f docker-compose.fintech.yml up -d postgres redis fintech-api

payments-only: ## Start only payments module
	@echo "💳 Starting payments module..."
	docker-compose -f docker-compose.fintech.yml up -d postgres redis fintech-api

yield-only: ## Start only yield module
	@echo "🌾 Starting yield module..."
	docker-compose -f docker-compose.fintech.yml up -d postgres redis fintech-api

trading-only: ## Start only trading module
	@echo "📈 Starting trading module..."
	docker-compose -f docker-compose.fintech.yml up -d postgres redis fintech-api

cards-only: ## Start only cards module
	@echo "💳 Starting cards module..."
	docker-compose -f docker-compose.fintech.yml up -d postgres redis fintech-api

# Quick commands
quick-start: build start ## Quick start (build and start)
quick-test: start-infra test ## Quick test (start infrastructure and run tests)
quick-clean: stop clean ## Quick clean (stop and clean)

# Production commands
prod-deploy: ## Deploy to production (placeholder)
	@echo "🚀 Production deployment..."
	@echo "This would deploy to production environment"

prod-backup: ## Create production backup (placeholder)
	@echo "💾 Creating production backup..."
	@echo "This would create a production backup"

# Version info
version: ## Show version information
	@echo "Fintech Platform v1.0.0"
	@echo "Go version: $(shell go version)"
	@echo "Docker version: $(shell docker --version)"
	@echo "Docker Compose version: $(shell docker-compose --version)"
