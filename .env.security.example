# Go Coffee Security Platform Environment Variables
# Copy this file to .env and update with your actual values

# =============================================================================
# CORE CONFIGURATION
# =============================================================================

# Environment
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=info
LOG_FORMAT=json

# =============================================================================
# SECURITY GATEWAY CONFIGURATION
# =============================================================================

# Server Configuration
SECURITY_GATEWAY_PORT=8080
SECURITY_GATEWAY_HOST=0.0.0.0
READ_TIMEOUT=30s
WRITE_TIMEOUT=30s
IDLE_TIMEOUT=120s

# =============================================================================
# ENCRYPTION & CRYPTOGRAPHY
# =============================================================================

# AES Encryption (Base64 encoded 32-byte key)
AES_KEY=your-base64-encoded-aes-256-key-change-this-in-production

# RSA Keys (PEM format)
RSA_PRIVATE_KEY=-----BEGIN RSA PRIVATE KEY-----\nYour RSA private key here\n-----END RSA PRIVATE KEY-----
RSA_PUBLIC_KEY=-----BEGIN PUBLIC KEY-----\nYour RSA public key here\n-----END PUBLIC KEY-----

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production-must-be-at-least-32-characters
JWT_EXPIRY=24h
REFRESH_TOKEN_EXPIRY=720h

# Password Hashing (Argon2)
ARGON2_TIME=1
ARGON2_MEMORY=64
ARGON2_THREADS=4
ARGON2_KEY_LENGTH=32

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================

# Redis for Rate Limiting and Caching
REDIS_URL=redis://localhost:6379
REDIS_DB=0
REDIS_PASSWORD=
REDIS_POOL_SIZE=10
REDIS_MAX_RETRIES=3
REDIS_DIAL_TIMEOUT=5s
REDIS_READ_TIMEOUT=3s
REDIS_WRITE_TIMEOUT=3s

# =============================================================================
# RATE LIMITING CONFIGURATION
# =============================================================================

# Global Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST_SIZE=20
RATE_LIMIT_CLEANUP_INTERVAL=1m

# IP-based Rate Limiting
IP_RATE_LIMIT_REQUESTS_PER_MINUTE=100
IP_RATE_LIMIT_BURST_SIZE=20

# User-based Rate Limiting
USER_RATE_LIMIT_REQUESTS_PER_MINUTE=200
USER_RATE_LIMIT_BURST_SIZE=40

# Endpoint-specific Rate Limiting
LOGIN_RATE_LIMIT_ATTEMPTS=5
LOGIN_RATE_LIMIT_WINDOW=15m
PAYMENT_RATE_LIMIT_REQUESTS=10
PAYMENT_RATE_LIMIT_WINDOW=1h

# =============================================================================
# WEB APPLICATION FIREWALL (WAF)
# =============================================================================

# WAF Configuration
WAF_ENABLED=true
WAF_BLOCK_SUSPICIOUS_IP=true
WAF_MAX_REQUEST_SIZE=10485760
WAF_ENABLE_GEO_BLOCKING=true
WAF_ENABLE_BOT_DETECTION=true

# Allowed/Blocked Countries (ISO 3166-1 alpha-2)
WAF_ALLOWED_COUNTRIES=US,CA,GB,DE,FR,UA,AU,JP
WAF_BLOCKED_COUNTRIES=CN,RU,KP,IR,SY

# WAF Rules Configuration
WAF_SQL_INJECTION_PROTECTION=true
WAF_XSS_PROTECTION=true
WAF_PATH_TRAVERSAL_PROTECTION=true
WAF_COMMAND_INJECTION_PROTECTION=true
WAF_LDAP_INJECTION_PROTECTION=true

# =============================================================================
# INPUT VALIDATION
# =============================================================================

# Validation Configuration
VALIDATION_MAX_INPUT_LENGTH=10000
VALIDATION_STRICT_MODE=true
VALIDATION_ENABLE_SANITIZATION=true
VALIDATION_ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx
VALIDATION_MAX_FILE_SIZE=10485760

# =============================================================================
# MULTI-FACTOR AUTHENTICATION (MFA)
# =============================================================================

# TOTP Configuration
MFA_TOTP_ISSUER=Go Coffee Security
MFA_TOTP_PERIOD=30
MFA_TOTP_DIGITS=6
MFA_TOTP_ALGORITHM=SHA1

# SMS Configuration
MFA_SMS_ENABLED=true
MFA_SMS_CODE_LENGTH=6
MFA_SMS_CODE_EXPIRY=5m
MFA_SMS_MAX_ATTEMPTS=3

# Email Configuration
MFA_EMAIL_ENABLED=true
MFA_EMAIL_CODE_LENGTH=6
MFA_EMAIL_CODE_EXPIRY=10m
MFA_EMAIL_MAX_ATTEMPTS=3

# Backup Codes
MFA_BACKUP_CODES_COUNT=10
MFA_BACKUP_CODE_LENGTH=8

# MFA Cooldown
MFA_COOLDOWN_PERIOD=15m
MFA_MAX_VERIFICATION_ATTEMPTS=3

# =============================================================================
# FRAUD DETECTION
# =============================================================================

# Fraud Detection Configuration
FRAUD_DETECTION_ENABLED=true
FRAUD_VELOCITY_CHECKS=true
FRAUD_AMOUNT_ANALYSIS=true
FRAUD_LOCATION_ANALYSIS=true
FRAUD_DEVICE_ANALYSIS=true
FRAUD_BEHAVIOR_ANALYSIS=true

# Fraud Thresholds
FRAUD_VELOCITY_WINDOW=1h
FRAUD_MAX_TRANSACTIONS_PER_HOUR=10
FRAUD_SUSPICIOUS_AMOUNT_MULTIPLIER=3.0
FRAUD_MAX_DISTANCE_KM=1000
FRAUD_MIN_TIME_BETWEEN_LOCATIONS=30m

# Risk Scoring
FRAUD_LOW_RISK_THRESHOLD=0.4
FRAUD_MEDIUM_RISK_THRESHOLD=0.6
FRAUD_HIGH_RISK_THRESHOLD=0.8

# =============================================================================
# SECURITY MONITORING
# =============================================================================

# Monitoring Configuration
SECURITY_MONITORING_ENABLED=true
SECURITY_REAL_TIME_MONITORING=true
SECURITY_THREAT_INTELLIGENCE=true
SECURITY_EVENT_RETENTION=720h
SECURITY_MAX_EVENTS_PER_MINUTE=1000

# Alert Thresholds
ALERT_FAILED_LOGIN_ATTEMPTS=5
ALERT_SUSPICIOUS_IP_REQUESTS=100
ALERT_HIGH_RISK_EVENTS=10
ALERT_TIME_WINDOW=5m
ALERT_CRITICAL_EVENT_THRESHOLD=1

# =============================================================================
# NOTIFICATION SERVICES
# =============================================================================

# Email Configuration (SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=Go Coffee Security

# SMS Configuration (Twilio)
SMS_PROVIDER=twilio
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_FROM_NUMBER=+**********

# Slack Notifications
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your/slack/webhook
SLACK_CHANNEL=#security-alerts

# =============================================================================
# MICROSERVICES CONFIGURATION
# =============================================================================

# Service URLs
AUTH_SERVICE_URL=http://localhost:8081
ORDER_SERVICE_URL=http://localhost:8082
PAYMENT_SERVICE_URL=http://localhost:8083
USER_SERVICE_URL=http://localhost:8084

# Service Health Check
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=30s
HEALTH_CHECK_TIMEOUT=5s

# Circuit Breaker
CIRCUIT_BREAKER_ENABLED=true
CIRCUIT_BREAKER_FAILURE_THRESHOLD=5
CIRCUIT_BREAKER_RECOVERY_TIMEOUT=30s
CIRCUIT_BREAKER_MAX_REQUESTS=3

# =============================================================================
# MONITORING & OBSERVABILITY
# =============================================================================

# Prometheus Metrics
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=9090
PROMETHEUS_METRICS_PATH=/metrics

# Jaeger Tracing
JAEGER_ENABLED=true
JAEGER_ENDPOINT=http://localhost:14268/api/traces
JAEGER_SAMPLER_TYPE=const
JAEGER_SAMPLER_PARAM=1

# Grafana
GRAFANA_ENABLED=true
GRAFANA_PORT=3000
GRAFANA_ADMIN_USER=admin
GRAFANA_ADMIN_PASSWORD=admin

# ELK Stack
ELASTICSEARCH_URL=http://localhost:9200
KIBANA_URL=http://localhost:5601
LOGSTASH_HOST=localhost
LOGSTASH_PORT=5044

# =============================================================================
# SECURITY HEADERS
# =============================================================================

# Content Security Policy
CSP_DEFAULT_SRC='self'
CSP_SCRIPT_SRC='self' 'unsafe-inline'
CSP_STYLE_SRC='self' 'unsafe-inline'
CSP_IMG_SRC='self' data: https:
CSP_FONT_SRC='self' https:
CSP_CONNECT_SRC='self' https:
CSP_FRAME_ANCESTORS='none'

# Other Security Headers
X_FRAME_OPTIONS=DENY
X_CONTENT_TYPE_OPTIONS=nosniff
X_XSS_PROTECTION=1; mode=block
STRICT_TRANSPORT_SECURITY=max-age=31536000; includeSubDomains; preload
REFERRER_POLICY=strict-origin-when-cross-origin
PERMISSIONS_POLICY=geolocation=(), microphone=(), camera=()

# =============================================================================
# CORS CONFIGURATION
# =============================================================================

# CORS Settings
CORS_ALLOWED_ORIGINS=*
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS,PATCH
CORS_ALLOWED_HEADERS=Origin,Content-Type,Authorization,X-Requested-With,X-Request-ID,X-Correlation-ID,X-Tenant-ID
CORS_EXPOSED_HEADERS=Content-Length,X-Request-ID,X-Correlation-ID,X-Rate-Limit-Remaining,X-Rate-Limit-Reset
CORS_ALLOW_CREDENTIALS=true
CORS_MAX_AGE=86400

# =============================================================================
# TLS/SSL CONFIGURATION
# =============================================================================

# TLS Configuration
TLS_ENABLED=false
TLS_CERT_FILE=/path/to/cert.pem
TLS_KEY_FILE=/path/to/key.pem
TLS_MIN_VERSION=1.2
TLS_CIPHER_SUITES=TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305,TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256

# =============================================================================
# THREAT INTELLIGENCE
# =============================================================================

# Threat Intelligence Configuration
THREAT_INTEL_ENABLED=true
THREAT_INTEL_PROVIDERS=local_blacklist,reputation_service

# Local Blacklist
THREAT_INTEL_BLACKLIST_FILE=/etc/security/blacklist.txt
THREAT_INTEL_BLACKLIST_REFRESH=1h

# External Threat Intelligence
THREAT_INTEL_API_URL=https://api.threatintel.com
THREAT_INTEL_API_KEY=your-threat-intel-api-key
THREAT_INTEL_REFRESH_INTERVAL=15m

# =============================================================================
# BACKUP & DISASTER RECOVERY
# =============================================================================

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_INTERVAL=24h
BACKUP_RETENTION_COUNT=7
BACKUP_STORAGE_PATH=/var/backups/security-gateway

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================

# Performance Settings
MAX_CONCURRENT_REQUESTS=1000
REQUEST_TIMEOUT=30s
IDLE_CONNECTION_TIMEOUT=90s
MAX_IDLE_CONNECTIONS=100
MAX_CONNECTIONS_PER_HOST=10

# =============================================================================
# DEVELOPMENT & TESTING
# =============================================================================

# Development Settings
ENABLE_DEBUG_ENDPOINTS=false
ENABLE_SWAGGER=true
ENABLE_PPROF=false

# Testing
ENABLE_TEST_ENDPOINTS=false
TEST_MODE=false

# =============================================================================
# COMPLIANCE
# =============================================================================

# PCI DSS Compliance
PCI_DSS_ENABLED=true
PCI_DSS_LEVEL=1

# GDPR Compliance
GDPR_ENABLED=true
DATA_RETENTION_PERIOD=2555d

# SOX Compliance
SOX_ENABLED=false
AUDIT_TRAIL_ENABLED=true

# =============================================================================
# NOTES
# =============================================================================

# 1. Never commit this file with real credentials to version control
# 2. Use strong, unique passwords and API keys
# 3. Rotate credentials regularly (every 90 days minimum)
# 4. Use environment-specific values for different deployments
# 5. Consider using a secrets management system for production (HashiCorp Vault, AWS Secrets Manager)
# 6. Validate all external API credentials before deployment
# 7. Monitor API usage and rate limits
# 8. Keep this file updated with new configuration options
# 9. Use proper key management for encryption keys
# 10. Enable all security features in production
# 11. Regularly review and update security configurations
# 12. Test disaster recovery procedures regularly
