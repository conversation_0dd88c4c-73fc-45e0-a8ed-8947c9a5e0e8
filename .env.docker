# =============================================================================
# DOCKER COMPOSE - ENVIRONMENT CONFIGURATION
# =============================================================================
# Environment variables for Docker Compose services
# =============================================================================

# =============================================================================
# DOCKER CONFIGURATION
# =============================================================================

# Docker Registry
DOCKER_REGISTRY=ghcr.io/dimajoyti
DOCKER_TAG=latest
DOCKER_BUILD_TARGET=production

# Build Configuration
DOCKER_BUILDKIT=1
COMPOSE_DOCKER_CLI_BUILD=1

# =============================================================================
# NETWORK CONFIGURATION
# =============================================================================

# Network Settings
COMPOSE_PROJECT_NAME=go-coffee
NETWORK_NAME=go-coffee-network
NETWORK_DRIVER=bridge

# =============================================================================
# POSTGRESQL CONFIGURATION
# =============================================================================

# PostgreSQL Database
POSTGRES_VERSION=15-alpine
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_DB=go_coffee
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres123
POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C

# PostgreSQL Performance
POSTGRES_SHARED_BUFFERS=256MB
POSTGRES_EFFECTIVE_CACHE_SIZE=1GB
POSTGRES_MAINTENANCE_WORK_MEM=64MB
POSTGRES_CHECKPOINT_COMPLETION_TARGET=0.9
POSTGRES_WAL_BUFFERS=16MB
POSTGRES_DEFAULT_STATISTICS_TARGET=100

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================

# Redis Cache
REDIS_VERSION=8-alpine
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=redis123
REDIS_DATABASES=16

# Redis Performance
REDIS_MAXMEMORY=512mb
REDIS_MAXMEMORY_POLICY=allkeys-lru
REDIS_SAVE_INTERVAL=900 1
REDIS_APPENDONLY=yes
REDIS_APPENDFSYNC=everysec

# =============================================================================
# KAFKA CONFIGURATION
# =============================================================================

# Kafka Cluster
KAFKA_VERSION=7.4.0
KAFKA_HOST=kafka
KAFKA_PORT=9092
KAFKA_EXTERNAL_PORT=29092

# Kafka Broker Configuration
KAFKA_BROKER_ID=1
KAFKA_ZOOKEEPER_CONNECT=zookeeper:2181
KAFKA_ADVERTISED_LISTENERS=PLAINTEXT://kafka:9092,PLAINTEXT_HOST://localhost:29092
KAFKA_LISTENER_SECURITY_PROTOCOL_MAP=PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
KAFKA_INTER_BROKER_LISTENER_NAME=PLAINTEXT
KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR=1
KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR=1
KAFKA_TRANSACTION_STATE_LOG_MIN_ISR=1

# Kafka Performance
KAFKA_NUM_PARTITIONS=3
KAFKA_DEFAULT_REPLICATION_FACTOR=1
KAFKA_LOG_RETENTION_HOURS=168
KAFKA_LOG_SEGMENT_BYTES=1073741824
KAFKA_LOG_RETENTION_CHECK_INTERVAL_MS=300000

# =============================================================================
# ZOOKEEPER CONFIGURATION
# =============================================================================

# Zookeeper
ZOOKEEPER_VERSION=7.4.0
ZOOKEEPER_HOST=zookeeper
ZOOKEEPER_PORT=2181
ZOOKEEPER_CLIENT_PORT=2181
ZOOKEEPER_TICK_TIME=2000

# =============================================================================
# MONITORING SERVICES
# =============================================================================

# Prometheus
PROMETHEUS_VERSION=latest
PROMETHEUS_HOST=prometheus
PROMETHEUS_PORT=9090
PROMETHEUS_CONFIG_PATH=./monitoring/prometheus/prometheus.yml
PROMETHEUS_STORAGE_RETENTION=15d

# Grafana
GRAFANA_VERSION=latest
GRAFANA_HOST=grafana
GRAFANA_PORT=3000
GRAFANA_ADMIN_USER=admin
GRAFANA_ADMIN_PASSWORD=admin123
GRAFANA_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource

# Jaeger
JAEGER_VERSION=latest
JAEGER_HOST=jaeger
JAEGER_PORT=16686
JAEGER_COLLECTOR_PORT=14268
JAEGER_AGENT_PORT=6831

# =============================================================================
# APPLICATION SERVICES
# =============================================================================

# API Gateway
API_GATEWAY_IMAGE=${DOCKER_REGISTRY}/go-coffee-api-gateway:${DOCKER_TAG}
API_GATEWAY_PORT=8080
API_GATEWAY_REPLICAS=2

# Producer Service
PRODUCER_IMAGE=${DOCKER_REGISTRY}/go-coffee-producer:${DOCKER_TAG}
PRODUCER_PORT=3000
PRODUCER_REPLICAS=3

# Consumer Service
CONSUMER_IMAGE=${DOCKER_REGISTRY}/go-coffee-consumer:${DOCKER_TAG}
CONSUMER_PORT=3001
CONSUMER_REPLICAS=2

# Streams Service
STREAMS_IMAGE=${DOCKER_REGISTRY}/go-coffee-streams:${DOCKER_TAG}
STREAMS_PORT=3002
STREAMS_REPLICAS=1

# AI Search Service
AI_SEARCH_IMAGE=${DOCKER_REGISTRY}/go-coffee-ai-search:${DOCKER_TAG}
AI_SEARCH_PORT=8092
AI_SEARCH_REPLICAS=2

# Auth Service
AUTH_SERVICE_IMAGE=${DOCKER_REGISTRY}/go-coffee-auth:${DOCKER_TAG}
AUTH_SERVICE_PORT=8091
AUTH_SERVICE_REPLICAS=2

# =============================================================================
# WEB3 SERVICES
# =============================================================================

# Web3 API Gateway
WEB3_API_GATEWAY_IMAGE=${DOCKER_REGISTRY}/go-coffee-web3-gateway:${DOCKER_TAG}
WEB3_API_GATEWAY_PORT=8081
WEB3_API_GATEWAY_REPLICAS=2

# Wallet Service
WALLET_SERVICE_IMAGE=${DOCKER_REGISTRY}/go-coffee-wallet:${DOCKER_TAG}
WALLET_SERVICE_PORT=8083
WALLET_SERVICE_REPLICAS=2

# DeFi Service
DEFI_SERVICE_IMAGE=${DOCKER_REGISTRY}/go-coffee-defi:${DOCKER_TAG}
DEFI_SERVICE_PORT=8082
DEFI_SERVICE_REPLICAS=1

# =============================================================================
# AI AGENTS
# =============================================================================

# Beverage Inventor Agent
BEVERAGE_INVENTOR_IMAGE=${DOCKER_REGISTRY}/go-coffee-beverage-inventor:${DOCKER_TAG}
BEVERAGE_INVENTOR_REPLICAS=1

# Inventory Manager Agent
INVENTORY_MANAGER_IMAGE=${DOCKER_REGISTRY}/go-coffee-inventory-manager:${DOCKER_TAG}
INVENTORY_MANAGER_REPLICAS=1

# Task Manager Agent
TASK_MANAGER_IMAGE=${DOCKER_REGISTRY}/go-coffee-task-manager:${DOCKER_TAG}
TASK_MANAGER_REPLICAS=1

# =============================================================================
# LOAD BALANCER
# =============================================================================

# Nginx Load Balancer
NGINX_VERSION=alpine
NGINX_HOST=nginx
NGINX_PORT=80
NGINX_SSL_PORT=443
NGINX_CONFIG_PATH=./nginx/nginx.conf

# =============================================================================
# VOLUMES & STORAGE
# =============================================================================

# Volume Configuration
POSTGRES_DATA_VOLUME=postgres_data
REDIS_DATA_VOLUME=redis_data
KAFKA_DATA_VOLUME=kafka_data
ZOOKEEPER_DATA_VOLUME=zookeeper_data
PROMETHEUS_DATA_VOLUME=prometheus_data
GRAFANA_DATA_VOLUME=grafana_data

# Backup Volumes
BACKUP_VOLUME=backup_data
LOG_VOLUME=log_data

# =============================================================================
# RESOURCE LIMITS
# =============================================================================

# Memory Limits
POSTGRES_MEMORY_LIMIT=1g
REDIS_MEMORY_LIMIT=512m
KAFKA_MEMORY_LIMIT=1g
ZOOKEEPER_MEMORY_LIMIT=512m

# Application Memory Limits
API_GATEWAY_MEMORY_LIMIT=256m
PRODUCER_MEMORY_LIMIT=256m
CONSUMER_MEMORY_LIMIT=256m
STREAMS_MEMORY_LIMIT=512m
AI_SEARCH_MEMORY_LIMIT=512m

# CPU Limits
POSTGRES_CPU_LIMIT=1.0
REDIS_CPU_LIMIT=0.5
KAFKA_CPU_LIMIT=1.0
APPLICATION_CPU_LIMIT=0.5

# =============================================================================
# HEALTH CHECKS
# =============================================================================

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30s
HEALTH_CHECK_TIMEOUT=10s
HEALTH_CHECK_RETRIES=3
HEALTH_CHECK_START_PERIOD=60s

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Logging Driver
LOGGING_DRIVER=json-file
LOGGING_MAX_SIZE=10m
LOGGING_MAX_FILE=3

# Log Levels
POSTGRES_LOG_LEVEL=info
REDIS_LOG_LEVEL=notice
KAFKA_LOG_LEVEL=INFO
APPLICATION_LOG_LEVEL=info

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Development Mode
DEVELOPMENT_MODE=true
ENABLE_DEBUG=true
ENABLE_HOT_RELOAD=true

# Port Mapping
ENABLE_PORT_MAPPING=true
HOST_NETWORK_MODE=false

# =============================================================================
# SECURITY SETTINGS
# =============================================================================

# Container Security
RUN_AS_NON_ROOT=true
READ_ONLY_ROOT_FILESYSTEM=false
NO_NEW_PRIVILEGES=true

# Network Security
ENABLE_NETWORK_ISOLATION=true
INTERNAL_NETWORK_ONLY=false
