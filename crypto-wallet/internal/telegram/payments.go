package telegram

import (
	"context"
	"fmt"
	"math/big"
	"strings"
	"time"

	tgbotapi "github.com/go-telegram-bot-api/telegram-bot-api/v5"

	"github.com/DimaJoyti/go-coffee/web3-wallet-backend/internal/ai"
	"github.com/DimaJ<PERSON>ti/go-coffee/web3-wallet-backend/pkg/models"
)

// PaymentRequest represents a payment request
type PaymentRequest struct {
	UserID        int64     `json:"user_id"`
	OrderID       string    `json:"order_id"`
	Amount        float64   `json:"amount_usd"`
	Currency      string    `json:"currency"`
	WalletAddress string    `json:"wallet_address"`
	CreatedAt     time.Time `json:"created_at"`
	ExpiresAt     time.Time `json:"expires_at"`
	Status        string    `json:"status"`
}

// CryptoPaymentInfo represents crypto payment information
type CryptoPaymentInfo struct {
	Currency      string  `json:"currency"`
	Amount        string  `json:"amount"`
	Address       string  `json:"address"`
	QRCode        string  `json:"qr_code,omitempty"`
	ExchangeRate  float64 `json:"exchange_rate"`
	NetworkFee    string  `json:"network_fee"`
	TotalRequired string  `json:"total_required"`
}

// processPaymentRequest processes a crypto payment request
func (b *Bot) processPaymentRequest(ctx context.Context, session *UserSession, currency string, amount float64) error {
	b.logger.Info(fmt.Sprintf("Processing payment request for user %d: %.2f USD in %s", session.UserID, amount, currency))

	// Generate payment address and details
	paymentInfo, err := b.generatePaymentInfo(ctx, currency, amount)
	if err != nil {
		return fmt.Errorf("failed to generate payment info: %w", err)
	}

	// Create payment request
	paymentReq := &PaymentRequest{
		UserID:        session.UserID,
		OrderID:       fmt.Sprintf("order_%d_%d", session.UserID, time.Now().Unix()),
		Amount:        amount,
		Currency:      currency,
		WalletAddress: paymentInfo.Address,
		CreatedAt:     time.Now(),
		ExpiresAt:     time.Now().Add(30 * time.Minute), // 30 minutes to pay
		Status:        "pending",
	}

	// Store payment request in session
	session.Context["payment_request"] = paymentReq
	session.Context["payment_info"] = paymentInfo

	// Send payment instructions
	return b.sendPaymentInstructions(session.ChatID, paymentInfo, paymentReq)
}

// generatePaymentInfo generates payment information for the specified currency
func (b *Bot) generatePaymentInfo(ctx context.Context, currency string, amountUSD float64) (*CryptoPaymentInfo, error) {
	// Get current exchange rates (mock implementation)
	exchangeRates := map[string]float64{
		"btc":  42000.0,  // 1 BTC = $42,000
		"eth":  2800.0,   // 1 ETH = $2,800
		"usdc": 1.0,      // 1 USDC = $1
		"usdt": 1.0,      // 1 USDT = $1
		"sol":  95.0,     // 1 SOL = $95
	}

	rate, exists := exchangeRates[strings.ToLower(currency)]
	if !exists {
		return nil, fmt.Errorf("unsupported currency: %s", currency)
	}

	// Calculate crypto amount
	cryptoAmount := amountUSD / rate

	// Generate wallet address (mock implementation)
	address := b.generateWalletAddress(currency)

	// Calculate network fees (mock implementation)
	networkFees := map[string]string{
		"btc":  "0.0001",
		"eth":  "0.002",
		"usdc": "0.002", // Same as ETH since it's ERC-20
		"usdt": "0.002", // Same as ETH since it's ERC-20
		"sol":  "0.00025",
	}

	fee := networkFees[strings.ToLower(currency)]
	
	return &CryptoPaymentInfo{
		Currency:      strings.ToUpper(currency),
		Amount:        fmt.Sprintf("%.8f", cryptoAmount),
		Address:       address,
		ExchangeRate:  rate,
		NetworkFee:    fee,
		TotalRequired: fmt.Sprintf("%.8f", cryptoAmount),
	}, nil
}

// generateWalletAddress generates a wallet address for the specified currency
func (b *Bot) generateWalletAddress(currency string) string {
	// Mock wallet addresses - in production, these would be generated by the wallet service
	addresses := map[string]string{
		"btc":  "******************************************",
		"eth":  "******************************************",
		"usdc": "******************************************",
		"usdt": "******************************************",
		"sol":  "7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU",
	}

	if addr, exists := addresses[strings.ToLower(currency)]; exists {
		return addr
	}

	// Fallback to a generic address
	return "******************************************"
}

// sendPaymentInstructions sends payment instructions to the user
func (b *Bot) sendPaymentInstructions(chatID int64, paymentInfo *CryptoPaymentInfo, paymentReq *PaymentRequest) error {
	instructionsText := fmt.Sprintf(`💳 *Оплата %s*

*Деталі платежу:*
• Сума: %s %s
• Курс: $%.2f за 1 %s
• Комісія мережі: %s %s

*Адреса для переказу:*
`+"`%s`"+`

⚠️ *Важливо:*
• Відправте точно %s %s
• Використовуйте тільки вказану адресу
• Платіж дійсний протягом 30 хвилин
• Після відправки натисніть "Підтвердити платіж"

*Статус:* Очікування платежу...`,
		paymentInfo.Currency,
		paymentInfo.Amount, paymentInfo.Currency,
		paymentInfo.ExchangeRate, paymentInfo.Currency,
		paymentInfo.NetworkFee, paymentInfo.Currency,
		paymentInfo.Address,
		paymentInfo.TotalRequired, paymentInfo.Currency,
	)

	keyboard := tgbotapi.NewInlineKeyboardMarkup(
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("✅ Підтвердити платіж", "confirm_payment"),
			tgbotapi.NewInlineKeyboardButtonData("📋 Копіювати адресу", "copy_address"),
		),
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("🔄 Перевірити статус", "check_payment_status"),
			tgbotapi.NewInlineKeyboardButtonData("❌ Скасувати", "cancel_payment"),
		),
	)

	return b.sendMessageWithKeyboard(chatID, instructionsText, keyboard)
}

// handlePaymentConfirmation handles payment confirmation
func (b *Bot) handlePaymentConfirmation(ctx context.Context, callback *tgbotapi.CallbackQuery, session *UserSession) {
	paymentReq, exists := session.Context["payment_request"].(*PaymentRequest)
	if !exists {
		b.sendMessage(session.ChatID, "❌ Платіжна інформація не знайдена. Почніть нове замовлення.")
		return
	}

	// Check if payment has expired
	if time.Now().After(paymentReq.ExpiresAt) {
		b.sendMessage(session.ChatID, "⏰ Час для оплати вичерпано. Почніть нове замовлення.")
		session.State = StateIdle
		delete(session.Context, "payment_request")
		delete(session.Context, "payment_info")
		return
	}

	// Simulate payment verification (in production, this would check blockchain)
	verified := b.verifyPayment(ctx, paymentReq)

	if verified {
		b.handleSuccessfulPayment(ctx, session, paymentReq)
	} else {
		b.handlePendingPayment(ctx, session, paymentReq)
	}
}

// verifyPayment verifies if payment has been received
func (b *Bot) verifyPayment(ctx context.Context, paymentReq *PaymentRequest) bool {
	// Mock verification - in production, this would check blockchain transactions
	// For demo purposes, randomly return true 30% of the time
	return time.Now().Unix()%10 < 3
}

// handleSuccessfulPayment handles successful payment
func (b *Bot) handleSuccessfulPayment(ctx context.Context, session *UserSession, paymentReq *PaymentRequest) {
	successText := fmt.Sprintf(`🎉 *Платіж успішно отримано!*

*Деталі транзакції:*
• ID замовлення: %s
• Сума: %.2f USD
• Валюта: %s
• Статус: ✅ Підтверджено

Ваше замовлення передано в роботу і буде готове через 5-10 хвилин.

Дякуємо за замовлення! ☕️`,
		paymentReq.OrderID,
		paymentReq.Amount,
		paymentReq.Currency,
	)

	keyboard := tgbotapi.NewInlineKeyboardMarkup(
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("📦 Мої замовлення", "my_orders"),
			tgbotapi.NewInlineKeyboardButtonData("☕️ Нове замовлення", "start_order"),
		),
	)

	b.sendMessageWithKeyboard(session.ChatID, successText, keyboard)

	// Clean up session
	session.State = StateIdle
	delete(session.Context, "payment_request")
	delete(session.Context, "payment_info")
	delete(session.Context, "pending_order")
}

// handlePendingPayment handles pending payment
func (b *Bot) handlePendingPayment(ctx context.Context, session *UserSession, paymentReq *PaymentRequest) {
	pendingText := fmt.Sprintf(`⏳ *Платіж ще не отримано*

Ми не знайшли ваш платіж на блокчейні. Це може зайняти кілька хвилин.

*Що робити:*
• Переконайтеся, що відправили правильну суму
• Перевірте адресу отримувача
• Зачекайте кілька хвилин для підтвердження

*Час до закінчення:* %s`,
		paymentReq.ExpiresAt.Sub(time.Now()).Round(time.Minute),
	)

	keyboard := tgbotapi.NewInlineKeyboardMarkup(
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("🔄 Перевірити знову", "confirm_payment"),
			tgbotapi.NewInlineKeyboardButtonData("📋 Показати адресу", "show_payment_address"),
		),
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("❌ Скасувати платіж", "cancel_payment"),
		),
	)

	b.sendMessageWithKeyboard(session.ChatID, pendingText, keyboard)
}

// handlePaymentStatusCheck handles payment status check
func (b *Bot) handlePaymentStatusCheck(ctx context.Context, callback *tgbotapi.CallbackQuery, session *UserSession) {
	paymentReq, exists := session.Context["payment_request"].(*PaymentRequest)
	if !exists {
		answerCallback := tgbotapi.NewCallbackWithAlert(callback.ID, "❌ Платіжна інформація не знайдена")
		b.api.Request(answerCallback)
		return
	}

	// Check payment status
	verified := b.verifyPayment(ctx, paymentReq)
	
	if verified {
		answerCallback := tgbotapi.NewCallbackWithAlert(callback.ID, "✅ Платіж підтверджено!")
		b.api.Request(answerCallback)
		b.handleSuccessfulPayment(ctx, session, paymentReq)
	} else {
		answerCallback := tgbotapi.NewCallbackWithAlert(callback.ID, "⏳ Платіж ще обробляється...")
		b.api.Request(answerCallback)
	}
}
