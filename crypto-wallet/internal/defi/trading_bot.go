package defi

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/DimaJoyti/go-coffee/web3-wallet-backend/pkg/logger"
	"github.com/DimaJoyti/go-coffee/web3-wallet-backend/pkg/redis"
	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

// TradingBot represents an automated trading bot
type TradingBot struct {
	ID          string              `json:"id"`
	Name        string              `json:"name"`
	Strategy    TradingStrategyType `json:"strategy"`
	Status      TradingBotStatus    `json:"status"`
	Config      TradingBotConfig    `json:"config"`
	Performance TradingPerformance  `json:"performance"`

	// Internal components
	logger            *logger.Logger
	cache             redis.Client
	arbitrageDetector *ArbitrageDetector
	yieldAggregator   *YieldAggregator

	// State
	activePositions map[string]*TradingPosition
	executionQueue  chan *TradingOrder
	mutex           sync.RWMutex
	stopChan        chan struct{}

	// Clients
	uniswapClient *UniswapClient
	oneInchClient *OneInchClient
	aaveClient    *AaveClient
}

// TradingBotStatus represents the status of a trading bot
type TradingBotStatus string

const (
	BotStatusActive  TradingBotStatus = "active"
	BotStatusPaused  TradingBotStatus = "paused"
	BotStatusStopped TradingBotStatus = "stopped"
	BotStatusError   TradingBotStatus = "error"
)

// TradingBotConfig represents trading bot configuration
type TradingBotConfig struct {
	MaxPositionSize   decimal.Decimal `json:"max_position_size"`
	MinProfitMargin   decimal.Decimal `json:"min_profit_margin"`
	MaxSlippage       decimal.Decimal `json:"max_slippage"`
	RiskTolerance     RiskLevel       `json:"risk_tolerance"`
	AutoCompound      bool            `json:"auto_compound"`
	MaxDailyTrades    int             `json:"max_daily_trades"`
	StopLossPercent   decimal.Decimal `json:"stop_loss_percent"`
	TakeProfitPercent decimal.Decimal `json:"take_profit_percent"`
	ExecutionDelay    time.Duration   `json:"execution_delay"`
}

// TradingPosition represents an active trading position
type TradingPosition struct {
	ID            string          `json:"id"`
	BotID         string          `json:"bot_id"`
	Type          PositionType    `json:"type"`
	Token         Token           `json:"token"`
	Amount        decimal.Decimal `json:"amount"`
	EntryPrice    decimal.Decimal `json:"entry_price"`
	CurrentPrice  decimal.Decimal `json:"current_price"`
	UnrealizedPnL decimal.Decimal `json:"unrealized_pnl"`
	StopLoss      decimal.Decimal `json:"stop_loss"`
	TakeProfit    decimal.Decimal `json:"take_profit"`
	OpenedAt      time.Time       `json:"opened_at"`
	UpdatedAt     time.Time       `json:"updated_at"`
}

// PositionType represents the type of trading position
type PositionType string

const (
	PositionTypeLong  PositionType = "long"
	PositionTypeShort PositionType = "short"
)

// TradingOrder represents a trading order
type TradingOrder struct {
	ID         string          `json:"id"`
	BotID      string          `json:"bot_id"`
	Type       OrderType       `json:"type"`
	Token      Token           `json:"token"`
	Amount     decimal.Decimal `json:"amount"`
	Price      decimal.Decimal `json:"price"`
	Status     OrderStatus     `json:"status"`
	CreatedAt  time.Time       `json:"created_at"`
	ExecutedAt *time.Time      `json:"executed_at,omitempty"`
}

// OrderType represents the type of trading order
type OrderType string

const (
	OrderTypeBuy   OrderType = "buy"
	OrderTypeSell  OrderType = "sell"
	OrderTypeSwap  OrderType = "swap"
	OrderTypeStake OrderType = "stake"
)

// OrderStatus represents the status of a trading order
type OrderStatus string

const (
	OrderStatusPending   OrderStatus = "pending"
	OrderStatusExecuting OrderStatus = "executing"
	OrderStatusExecuted  OrderStatus = "executed"
	OrderStatusFailed    OrderStatus = "failed"
	OrderStatusCancelled OrderStatus = "cancelled"
)

// NewTradingBot creates a new trading bot
func NewTradingBot(
	name string,
	strategy TradingStrategyType,
	config TradingBotConfig,
	logger *logger.Logger,
	cache redis.Client,
	arbitrageDetector *ArbitrageDetector,
	yieldAggregator *YieldAggregator,
	uniswapClient *UniswapClient,
	oneInchClient *OneInchClient,
	aaveClient *AaveClient,
) *TradingBot {
	return &TradingBot{
		ID:                uuid.New().String(),
		Name:              name,
		Strategy:          strategy,
		Status:            BotStatusStopped,
		Config:            config,
		Performance:       TradingPerformance{},
		logger:            logger.Named("trading-bot"),
		cache:             cache,
		arbitrageDetector: arbitrageDetector,
		yieldAggregator:   yieldAggregator,
		activePositions:   make(map[string]*TradingPosition),
		executionQueue:    make(chan *TradingOrder, 100),
		stopChan:          make(chan struct{}),
		uniswapClient:     uniswapClient,
		oneInchClient:     oneInchClient,
		aaveClient:        aaveClient,
	}
}

// Start starts the trading bot
func (tb *TradingBot) Start(ctx context.Context) error {
	tb.mutex.Lock()
	defer tb.mutex.Unlock()

	if tb.Status == BotStatusActive {
		return fmt.Errorf("bot is already active")
	}

	tb.Status = BotStatusActive
	tb.logger.Info("Starting trading bot", "id", tb.ID, "name", tb.Name, "strategy", tb.Strategy)

	// Start the main trading loop
	go tb.tradingLoop(ctx)

	// Start the order execution loop
	go tb.orderExecutionLoop(ctx)

	// Start position monitoring
	go tb.positionMonitoringLoop(ctx)

	return nil
}

// Stop stops the trading bot
func (tb *TradingBot) Stop() error {
	tb.mutex.Lock()
	defer tb.mutex.Unlock()

	if tb.Status == BotStatusStopped {
		return fmt.Errorf("bot is already stopped")
	}

	tb.Status = BotStatusStopped
	tb.logger.Info("Stopping trading bot", "id", tb.ID)

	close(tb.stopChan)
	return nil
}

// Pause pauses the trading bot
func (tb *TradingBot) Pause() error {
	tb.mutex.Lock()
	defer tb.mutex.Unlock()

	if tb.Status != BotStatusActive {
		return fmt.Errorf("bot is not active")
	}

	tb.Status = BotStatusPaused
	tb.logger.Info("Pausing trading bot", "id", tb.ID)

	return nil
}

// Resume resumes the trading bot
func (tb *TradingBot) Resume() error {
	tb.mutex.Lock()
	defer tb.mutex.Unlock()

	if tb.Status != BotStatusPaused {
		return fmt.Errorf("bot is not paused")
	}

	tb.Status = BotStatusActive
	tb.logger.Info("Resuming trading bot", "id", tb.ID)

	return nil
}

// GetStatus returns the current status of the trading bot
func (tb *TradingBot) GetStatus() TradingBotStatus {
	tb.mutex.RLock()
	defer tb.mutex.RUnlock()
	return tb.Status
}

// GetPerformance returns the performance metrics of the trading bot
func (tb *TradingBot) GetPerformance() TradingPerformance {
	tb.mutex.RLock()
	defer tb.mutex.RUnlock()
	return tb.Performance
}

// GetActivePositions returns the active trading positions
func (tb *TradingBot) GetActivePositions() []*TradingPosition {
	tb.mutex.RLock()
	defer tb.mutex.RUnlock()

	positions := make([]*TradingPosition, 0, len(tb.activePositions))
	for _, position := range tb.activePositions {
		positions = append(positions, position)
	}

	return positions
}

// tradingLoop runs the main trading logic
func (tb *TradingBot) tradingLoop(ctx context.Context) {
	ticker := time.NewTicker(time.Second * 30) // Check every 30 seconds
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-tb.stopChan:
			return
		case <-ticker.C:
			if tb.GetStatus() == BotStatusActive {
				tb.executeStrategy(ctx)
			}
		}
	}
}

// executeStrategy executes the trading strategy
func (tb *TradingBot) executeStrategy(ctx context.Context) {
	switch tb.Strategy {
	case StrategyTypeArbitrage:
		tb.executeArbitrageStrategy(ctx)
	case StrategyTypeYieldFarming:
		tb.executeYieldFarmingStrategy(ctx)
	case StrategyTypeDCA:
		tb.executeDCAStrategy(ctx)
	case StrategyTypeGridTrading:
		tb.executeGridTradingStrategy(ctx)
	case StrategyTypeRebalancing:
		tb.executeRebalancingStrategy(ctx)
	default:
		tb.logger.Warn("Unknown strategy type", "strategy", tb.Strategy)
	}
}

// executeArbitrageStrategy executes arbitrage trading strategy
func (tb *TradingBot) executeArbitrageStrategy(ctx context.Context) {
	opportunities, err := tb.arbitrageDetector.GetOpportunities(ctx)
	if err != nil {
		tb.logger.Error("Failed to get arbitrage opportunities", "error", err)
		return
	}

	for _, opp := range opportunities {
		// Check if opportunity meets our criteria
		if opp.ProfitMargin.LessThan(tb.Config.MinProfitMargin) {
			continue
		}

		if opp.Risk == RiskLevelHigh && tb.Config.RiskTolerance != RiskLevelHigh {
			continue
		}

		// Create buy order for source exchange
		buyOrder := &TradingOrder{
			ID:        uuid.New().String(),
			BotID:     tb.ID,
			Type:      OrderTypeBuy,
			Token:     opp.Token,
			Amount:    opp.Volume,
			Price:     opp.SourcePrice,
			Status:    OrderStatusPending,
			CreatedAt: time.Now(),
		}

		// Queue the order for execution
		select {
		case tb.executionQueue <- buyOrder:
			tb.logger.Info("Queued arbitrage buy order",
				"order_id", buyOrder.ID,
				"token", opp.Token.Symbol,
				"amount", opp.Volume,
				"profit_margin", opp.ProfitMargin)
		default:
			tb.logger.Warn("Execution queue full, skipping order")
		}
	}
}

// executeYieldFarmingStrategy executes yield farming strategy
func (tb *TradingBot) executeYieldFarmingStrategy(ctx context.Context) {
	opportunities, err := tb.yieldAggregator.GetBestOpportunities(ctx, 5)
	if err != nil {
		tb.logger.Error("Failed to get yield opportunities", "error", err)
		return
	}

	for _, opp := range opportunities {
		// Check if opportunity meets our criteria
		if opp.APY.LessThan(decimal.NewFromFloat(0.05)) { // 5% minimum APY
			continue
		}

		if opp.Risk == RiskLevelHigh && tb.Config.RiskTolerance != RiskLevelHigh {
			continue
		}

		// Create stake order
		stakeOrder := &TradingOrder{
			ID:        uuid.New().String(),
			BotID:     tb.ID,
			Type:      OrderTypeStake,
			Token:     opp.Pool.Token0,                                      // Simplified - use first token
			Amount:    tb.Config.MaxPositionSize.Div(decimal.NewFromInt(2)), // Use half of max position
			Price:     decimal.Zero,                                         // Not applicable for staking
			Status:    OrderStatusPending,
			CreatedAt: time.Now(),
		}

		select {
		case tb.executionQueue <- stakeOrder:
			tb.logger.Info("Queued yield farming order",
				"order_id", stakeOrder.ID,
				"protocol", opp.Protocol,
				"apy", opp.APY)
		default:
			tb.logger.Warn("Execution queue full, skipping order")
		}

		break // Only take the best opportunity for now
	}
}

// executeDCAStrategy executes Dollar Cost Averaging strategy
func (tb *TradingBot) executeDCAStrategy(ctx context.Context) {
	// Simple DCA implementation - buy COFFEE tokens regularly
	coffeeToken := Token{
		Address:  "******************************************", // Coffee token address
		Symbol:   "COFFEE",
		Name:     "Coffee Token",
		Decimals: 18,
		Chain:    ChainEthereum,
	}

	// Calculate DCA amount (1% of max position size)
	dcaAmount := tb.Config.MaxPositionSize.Div(decimal.NewFromInt(100))

	buyOrder := &TradingOrder{
		ID:        uuid.New().String(),
		BotID:     tb.ID,
		Type:      OrderTypeBuy,
		Token:     coffeeToken,
		Amount:    dcaAmount,
		Price:     decimal.Zero, // Market price
		Status:    OrderStatusPending,
		CreatedAt: time.Now(),
	}

	select {
	case tb.executionQueue <- buyOrder:
		tb.logger.Info("Queued DCA buy order",
			"order_id", buyOrder.ID,
			"token", coffeeToken.Symbol,
			"amount", dcaAmount)
	default:
		tb.logger.Warn("Execution queue full, skipping DCA order")
	}
}

// executeGridTradingStrategy executes grid trading strategy
func (tb *TradingBot) executeGridTradingStrategy(ctx context.Context) {
	// Simplified grid trading for COFFEE token
	// In real implementation, maintain price grids and place orders at different levels

	tb.logger.Debug("Grid trading strategy not fully implemented yet")
}

// executeRebalancingStrategy executes portfolio rebalancing strategy
func (tb *TradingBot) executeRebalancingStrategy(ctx context.Context) {
	// Simplified rebalancing - ensure Coffee tokens are 20% of portfolio

	tb.logger.Debug("Rebalancing strategy not fully implemented yet")
}

// orderExecutionLoop processes orders from the execution queue
func (tb *TradingBot) orderExecutionLoop(ctx context.Context) {
	for {
		select {
		case <-ctx.Done():
			return
		case <-tb.stopChan:
			return
		case order := <-tb.executionQueue:
			tb.executeOrder(ctx, order)
		}
	}
}

// executeOrder executes a trading order
func (tb *TradingBot) executeOrder(ctx context.Context, order *TradingOrder) {
	tb.logger.Info("Executing order",
		"order_id", order.ID,
		"type", order.Type,
		"token", order.Token.Symbol,
		"amount", order.Amount)

	order.Status = OrderStatusExecuting

	// Add execution delay to avoid MEV attacks
	if tb.Config.ExecutionDelay > 0 {
		time.Sleep(tb.Config.ExecutionDelay)
	}

	var err error
	switch order.Type {
	case OrderTypeBuy:
		err = tb.executeBuyOrder(ctx, order)
	case OrderTypeSell:
		err = tb.executeSellOrder(ctx, order)
	case OrderTypeSwap:
		err = tb.executeSwapOrder(ctx, order)
	case OrderTypeStake:
		err = tb.executeStakeOrder(ctx, order)
	default:
		err = fmt.Errorf("unknown order type: %s", order.Type)
	}

	if err != nil {
		order.Status = OrderStatusFailed
		tb.logger.Error("Failed to execute order",
			"order_id", order.ID,
			"error", err)
		tb.updatePerformance(false, decimal.Zero)
	} else {
		order.Status = OrderStatusExecuted
		now := time.Now()
		order.ExecutedAt = &now
		tb.logger.Info("Successfully executed order", "order_id", order.ID)
		tb.updatePerformance(true, order.Amount)
	}
}

// executeBuyOrder executes a buy order
func (tb *TradingBot) executeBuyOrder(ctx context.Context, order *TradingOrder) error {
	// Get best price from 1inch
	quote, err := tb.oneInchClient.GetSwapQuote(ctx, &GetSwapQuoteRequest{
		TokenIn:  "0xA0b86a33E6441b8C4505B6B8C0E4F7c3C4b5C8E1", // USDC
		TokenOut: order.Token.Address,
		AmountIn: order.Amount,
		Chain:    order.Token.Chain,
		Slippage: tb.Config.MaxSlippage,
	})
	if err != nil {
		return fmt.Errorf("failed to get swap quote: %w", err)
	}

	// Check if price impact is acceptable
	if quote.PriceImpact.GreaterThan(tb.Config.MaxSlippage) {
		return fmt.Errorf("price impact too high: %s", quote.PriceImpact)
	}

	// Execute the swap (mock implementation)
	tb.logger.Info("Executing buy order",
		"token", order.Token.Symbol,
		"amount_in", quote.AmountIn,
		"amount_out", quote.AmountOut,
		"price_impact", quote.PriceImpact)

	// Create position
	position := &TradingPosition{
		ID:           uuid.New().String(),
		BotID:        tb.ID,
		Type:         PositionTypeLong,
		Token:        order.Token,
		Amount:       quote.AmountOut,
		EntryPrice:   quote.AmountIn.Div(quote.AmountOut),
		CurrentPrice: quote.AmountIn.Div(quote.AmountOut),
		StopLoss:     tb.calculateStopLoss(quote.AmountIn.Div(quote.AmountOut)),
		TakeProfit:   tb.calculateTakeProfit(quote.AmountIn.Div(quote.AmountOut)),
		OpenedAt:     time.Now(),
		UpdatedAt:    time.Now(),
	}

	tb.mutex.Lock()
	tb.activePositions[position.ID] = position
	tb.mutex.Unlock()

	return nil
}

// executeSellOrder executes a sell order
func (tb *TradingBot) executeSellOrder(ctx context.Context, order *TradingOrder) error {
	// Similar to buy order but in reverse
	tb.logger.Info("Executing sell order", "token", order.Token.Symbol, "amount", order.Amount)

	// Mock implementation
	return nil
}

// executeSwapOrder executes a swap order
func (tb *TradingBot) executeSwapOrder(ctx context.Context, order *TradingOrder) error {
	tb.logger.Info("Executing swap order", "token", order.Token.Symbol, "amount", order.Amount)

	// Mock implementation
	return nil
}

// executeStakeOrder executes a stake order
func (tb *TradingBot) executeStakeOrder(ctx context.Context, order *TradingOrder) error {
	tb.logger.Info("Executing stake order", "token", order.Token.Symbol, "amount", order.Amount)

	// Mock implementation - would interact with staking contracts
	return nil
}

// positionMonitoringLoop monitors active positions
func (tb *TradingBot) positionMonitoringLoop(ctx context.Context) {
	ticker := time.NewTicker(time.Minute * 5) // Check every 5 minutes
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-tb.stopChan:
			return
		case <-ticker.C:
			if tb.GetStatus() == BotStatusActive {
				tb.monitorPositions(ctx)
			}
		}
	}
}

// monitorPositions monitors active positions for stop loss/take profit
func (tb *TradingBot) monitorPositions(ctx context.Context) {
	tb.mutex.RLock()
	positions := make([]*TradingPosition, 0, len(tb.activePositions))
	for _, position := range tb.activePositions {
		positions = append(positions, position)
	}
	tb.mutex.RUnlock()

	for _, position := range positions {
		// Get current price (mock implementation)
		currentPrice := position.EntryPrice.Mul(decimal.NewFromFloat(1.02)) // +2% mock price change

		// Update position
		tb.mutex.Lock()
		position.CurrentPrice = currentPrice
		position.UnrealizedPnL = currentPrice.Sub(position.EntryPrice).Mul(position.Amount)
		position.UpdatedAt = time.Now()
		tb.mutex.Unlock()

		// Check stop loss
		if currentPrice.LessThanOrEqual(position.StopLoss) {
			tb.logger.Info("Stop loss triggered",
				"position_id", position.ID,
				"current_price", currentPrice,
				"stop_loss", position.StopLoss)
			tb.closePosition(ctx, position)
		}

		// Check take profit
		if currentPrice.GreaterThanOrEqual(position.TakeProfit) {
			tb.logger.Info("Take profit triggered",
				"position_id", position.ID,
				"current_price", currentPrice,
				"take_profit", position.TakeProfit)
			tb.closePosition(ctx, position)
		}
	}
}

// closePosition closes a trading position
func (tb *TradingBot) closePosition(ctx context.Context, position *TradingPosition) {
	tb.mutex.Lock()
	delete(tb.activePositions, position.ID)
	tb.mutex.Unlock()

	// Create sell order to close position
	sellOrder := &TradingOrder{
		ID:        uuid.New().String(),
		BotID:     tb.ID,
		Type:      OrderTypeSell,
		Token:     position.Token,
		Amount:    position.Amount,
		Price:     position.CurrentPrice,
		Status:    OrderStatusPending,
		CreatedAt: time.Now(),
	}

	select {
	case tb.executionQueue <- sellOrder:
		tb.logger.Info("Queued position close order",
			"position_id", position.ID,
			"pnl", position.UnrealizedPnL)
	default:
		tb.logger.Warn("Failed to queue position close order")
	}
}

// Helper methods

func (tb *TradingBot) calculateStopLoss(entryPrice decimal.Decimal) decimal.Decimal {
	return entryPrice.Mul(decimal.NewFromFloat(1).Sub(tb.Config.StopLossPercent))
}

func (tb *TradingBot) calculateTakeProfit(entryPrice decimal.Decimal) decimal.Decimal {
	return entryPrice.Mul(decimal.NewFromFloat(1).Add(tb.Config.TakeProfitPercent))
}

func (tb *TradingBot) updatePerformance(success bool, amount decimal.Decimal) {
	tb.mutex.Lock()
	defer tb.mutex.Unlock()

	tb.Performance.TotalTrades++
	if success {
		tb.Performance.WinningTrades++
	} else {
		tb.Performance.LosingTrades++
	}

	tb.Performance.WinRate = decimal.NewFromInt(int64(tb.Performance.WinningTrades)).
		Div(decimal.NewFromInt(int64(tb.Performance.TotalTrades)))

	tb.Performance.LastUpdated = time.Now()
}
