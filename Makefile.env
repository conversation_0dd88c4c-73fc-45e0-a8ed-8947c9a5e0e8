# =============================================================================
# Go Coffee - Environment Management Makefile
# =============================================================================
# This Makefile provides commands for managing environment files and configuration
# =============================================================================

.PHONY: help env-setup env-validate env-test env-copy env-clean env-backup env-restore

# Default target
help: ## Show this help message
	@echo "🔧 Go Coffee Environment Management"
	@echo "=================================="
	@echo ""
	@echo "Available commands:"
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "  \033[36m%-20s\033[0m %s\n", $$1, $$2}'
	@echo ""
	@echo "Environment files:"
	@echo "  .env                 - Main environment file"
	@echo "  .env.development     - Development environment"
	@echo "  .env.production      - Production environment"
	@echo "  .env.docker          - Docker Compose environment"
	@echo "  .env.ai-search       - AI Search Engine specific"
	@echo "  .env.web3            - Web3 services specific"
	@echo "  .env.example         - Template file"
	@echo ""

# Environment setup
env-setup: ## Setup environment files for development
	@echo "🔧 Setting up environment files..."
	@if [ ! -f .env ]; then \
		echo "📁 Creating .env from .env.example..."; \
		cp .env.example .env; \
		echo "✅ Created .env file"; \
	else \
		echo "ℹ️  .env file already exists"; \
	fi
	@if [ ! -f .env.local ]; then \
		echo "📁 Creating .env.local..."; \
		touch .env.local; \
		echo "# Local environment overrides" > .env.local; \
		echo "# Add your local-specific variables here" >> .env.local; \
		echo "✅ Created .env.local file"; \
	else \
		echo "ℹ️  .env.local file already exists"; \
	fi
	@echo "🎉 Environment setup completed!"
	@echo ""
	@echo "📝 Next steps:"
	@echo "  1. Edit .env file with your configuration"
	@echo "  2. Add local overrides to .env.local"
	@echo "  3. Run 'make env-validate' to check configuration"

env-validate: ## Validate environment configuration
	@echo "🔍 Validating environment configuration..."
	@go run cmd/config-test/main.go validate
	@echo "✅ Environment validation completed!"

env-test: ## Test environment configuration loading
	@echo "🧪 Testing environment configuration..."
	@go run cmd/config-test/main.go
	@echo "✅ Environment test completed!"

env-copy: ## Copy environment files for different environments
	@echo "📋 Copying environment files..."
	@echo "Select target environment:"
	@echo "  1) Development"
	@echo "  2) Staging"
	@echo "  3) Production"
	@echo "  4) Docker"
	@read -p "Enter choice (1-4): " choice; \
	case $$choice in \
		1) cp .env.development .env && echo "✅ Copied development environment"; ;; \
		2) cp .env.example .env.staging && echo "✅ Created staging environment template"; ;; \
		3) cp .env.production .env && echo "⚠️  Copied production environment (BE CAREFUL!)"; ;; \
		4) cp .env.docker .env && echo "✅ Copied Docker environment"; ;; \
		*) echo "❌ Invalid choice"; exit 1; ;; \
	esac

env-clean: ## Clean up environment files (keeps .env.example)
	@echo "🧹 Cleaning up environment files..."
	@echo "⚠️  This will remove all .env files except .env.example"
	@read -p "Are you sure? (y/N): " confirm; \
	if [ "$$confirm" = "y" ] || [ "$$confirm" = "Y" ]; then \
		rm -f .env .env.local .env.development.local .env.production.local; \
		echo "✅ Environment files cleaned"; \
	else \
		echo "❌ Operation cancelled"; \
	fi

env-backup: ## Backup current environment files
	@echo "💾 Backing up environment files..."
	@mkdir -p backups/env
	@timestamp=$$(date +%Y%m%d_%H%M%S); \
	if [ -f .env ]; then cp .env backups/env/.env.$$timestamp; echo "✅ Backed up .env"; fi; \
	if [ -f .env.local ]; then cp .env.local backups/env/.env.local.$$timestamp; echo "✅ Backed up .env.local"; fi; \
	if [ -f .env.production ]; then cp .env.production backups/env/.env.production.$$timestamp; echo "✅ Backed up .env.production"; fi; \
	echo "📁 Backups saved to backups/env/"

env-restore: ## Restore environment files from backup
	@echo "🔄 Restoring environment files from backup..."
	@if [ ! -d backups/env ]; then \
		echo "❌ No backup directory found"; \
		exit 1; \
	fi
	@echo "Available backups:"
	@ls -la backups/env/ | grep -E '\.(env|local)' | awk '{print "  " $$9}'
	@read -p "Enter backup filename to restore: " filename; \
	if [ -f "backups/env/$$filename" ]; then \
		cp "backups/env/$$filename" .env; \
		echo "✅ Restored $$filename to .env"; \
	else \
		echo "❌ Backup file not found"; \
	fi

# Environment-specific commands
env-dev: ## Switch to development environment
	@echo "🔧 Switching to development environment..."
	@cp .env.development .env
	@echo "✅ Switched to development environment"
	@make env-validate

env-prod: ## Switch to production environment (with confirmation)
	@echo "⚠️  Switching to PRODUCTION environment"
	@echo "This will overwrite your current .env file!"
	@read -p "Are you sure? Type 'PRODUCTION' to confirm: " confirm; \
	if [ "$$confirm" = "PRODUCTION" ]; then \
		cp .env.production .env; \
		echo "✅ Switched to production environment"; \
		make env-validate; \
	else \
		echo "❌ Operation cancelled"; \
	fi

env-docker: ## Switch to Docker environment
	@echo "🐳 Switching to Docker environment..."
	@cp .env.docker .env
	@echo "✅ Switched to Docker environment"
	@make env-validate

# Security commands
env-check-secrets: ## Check for exposed secrets in environment files
	@echo "🔒 Checking for exposed secrets..."
	@echo "Checking for default/placeholder values that should be changed:"
	@echo ""
	@if grep -q "your-.*-key\|your-.*-secret\|your-.*-password\|your-.*-token" .env 2>/dev/null; then \
		echo "⚠️  Found placeholder values in .env:"; \
		grep --color=always "your-.*-key\|your-.*-secret\|your-.*-password\|your-.*-token" .env || true; \
		echo ""; \
	fi
	@if grep -q "postgres\|admin\|password\|secret" .env 2>/dev/null; then \
		echo "⚠️  Found potentially weak credentials in .env:"; \
		grep --color=always -i "password.*=.*postgres\|password.*=.*admin\|password.*=.*password\|secret.*=.*secret" .env || true; \
		echo ""; \
	fi
	@echo "✅ Security check completed"

env-generate-secrets: ## Generate secure random secrets for environment
	@echo "🔐 Generating secure secrets..."
	@echo "JWT_SECRET=$$(openssl rand -hex 32)"
	@echo "API_KEY_SECRET=$$(openssl rand -hex 24)"
	@echo "WEBHOOK_SECRET=$$(openssl rand -hex 24)"
	@echo "ENCRYPTION_KEY=$$(openssl rand -hex 16)"
	@echo ""
	@echo "💡 Copy these values to your .env file"

# Docker-related environment commands
env-docker-up: ## Start services with Docker using environment files
	@echo "🐳 Starting Docker services with environment configuration..."
	@docker-compose --env-file .env.docker up -d
	@echo "✅ Docker services started"

env-docker-down: ## Stop Docker services
	@echo "🐳 Stopping Docker services..."
	@docker-compose --env-file .env.docker down
	@echo "✅ Docker services stopped"

env-docker-logs: ## Show Docker services logs
	@echo "📋 Docker services logs..."
	@docker-compose --env-file .env.docker logs -f

# Utility commands
env-show: ## Show current environment configuration (without secrets)
	@echo "📋 Current environment configuration:"
	@echo "===================================="
	@go run cmd/config-test/main.go

env-export: ## Export environment configuration to JSON
	@echo "📤 Exporting environment configuration..."
	@go run cmd/config-test/main.go export

env-diff: ## Show differences between environment files
	@echo "📊 Environment files comparison:"
	@echo "==============================="
	@if [ -f .env ] && [ -f .env.development ]; then \
		echo "Differences between .env and .env.development:"; \
		diff -u .env .env.development || true; \
		echo ""; \
	fi
	@if [ -f .env ] && [ -f .env.production ]; then \
		echo "Differences between .env and .env.production:"; \
		diff -u .env .env.production || true; \
		echo ""; \
	fi

env-template: ## Create environment template from current .env
	@echo "📝 Creating environment template..."
	@if [ -f .env ]; then \
		sed 's/=.*/=your-value-here/' .env > .env.template; \
		echo "✅ Created .env.template"; \
	else \
		echo "❌ No .env file found"; \
	fi

# Build and test with environment
build-with-env: ## Build application with environment validation
	@echo "🔨 Building application with environment validation..."
	@make env-validate
	@go build -o bin/go-coffee ./cmd/...
	@echo "✅ Build completed with environment validation"

test-with-env: ## Run tests with environment configuration
	@echo "🧪 Running tests with environment configuration..."
	@make env-validate
	@go test ./...
	@echo "✅ Tests completed"

# Documentation
env-docs: ## Generate environment documentation
	@echo "📚 Generating environment documentation..."
	@echo "# Go Coffee Environment Variables" > ENV_DOCS.md
	@echo "" >> ENV_DOCS.md
	@echo "This document describes all environment variables used in Go Coffee." >> ENV_DOCS.md
	@echo "" >> ENV_DOCS.md
	@echo "## Core Application Settings" >> ENV_DOCS.md
	@grep -E "^[A-Z_]+=.*" .env.example | head -20 >> ENV_DOCS.md || true
	@echo "✅ Environment documentation generated in ENV_DOCS.md"

# Status check
env-status: ## Show environment status and health
	@echo "📊 Environment Status"
	@echo "===================="
	@echo "Current environment files:"
	@ls -la .env* 2>/dev/null || echo "No .env files found"
	@echo ""
	@echo "Environment variable count:"
	@if [ -f .env ]; then \
		echo "  .env: $$(grep -c '^[A-Z_]*=' .env 2>/dev/null || echo 0) variables"; \
	fi
	@if [ -f .env.local ]; then \
		echo "  .env.local: $$(grep -c '^[A-Z_]*=' .env.local 2>/dev/null || echo 0) variables"; \
	fi
	@echo ""
	@make env-check-secrets
