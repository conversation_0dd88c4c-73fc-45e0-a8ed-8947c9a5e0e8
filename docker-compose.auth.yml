version: '3.8'

services:
  # Redis for auth service
  auth-redis:
    image: redis:7-alpine
    container_name: auth-redis
    restart: unless-stopped
    ports:
      - "6380:6379"
    volumes:
      - auth_redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3
    networks:
      - auth-network

  # Auth Service
  auth-service:
    build:
      context: .
      dockerfile: cmd/auth-service/Dockerfile
    container_name: auth-service
    restart: unless-stopped
    ports:
      - "8080:8080"   # HTTP API
      - "50053:50053" # gRPC API
    environment:
      - REDIS_URL=redis://auth-redis:6379
      - JWT_SECRET=${JWT_SECRET:-your-super-secret-jwt-key-change-in-production}
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - ENVIRONMENT=${ENVIRONMENT:-development}
    volumes:
      - ./cmd/auth-service/config:/app/config
      - ./logs:/app/logs
    depends_on:
      auth-redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - auth-network

  # Prometheus for monitoring (optional)
  auth-prometheus:
    image: prom/prometheus:latest
    container_name: auth-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - auth_prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - auth-network

  # Grafana for visualization (optional)
  auth-grafana:
    image: grafana/grafana:latest
    container_name: auth-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - auth_grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
    networks:
      - auth-network

  # Jaeger for distributed tracing (optional)
  auth-jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: auth-jaeger
    restart: unless-stopped
    ports:
      - "16686:16686"  # Jaeger UI
      - "14268:14268"  # HTTP collector
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    networks:
      - auth-network

volumes:
  auth_redis_data:
    driver: local
  auth_prometheus_data:
    driver: local
  auth_grafana_data:
    driver: local

networks:
  auth-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
