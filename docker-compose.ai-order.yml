version: '3.8'

services:
  # Redis for AI context and caching
  redis:
    image: redis:7-alpine
    container_name: redis-ai-order
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - ai-order-network

  # AI Order Service
  ai-order-service:
    build:
      context: .
      dockerfile: docker/Dockerfile.ai-order
    container_name: ai-order-service
    ports:
      - "50051:50051"
    environment:
      - GRPC_PORT=50051
      - REDIS_URL=redis://redis:6379
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - OLLAMA_BASE_URL=${OLLAMA_BASE_URL:-http://localhost:11434}
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "grpc_health_probe", "-addr=:50051"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - ai-order-network
    volumes:
      - ./logs:/app/logs

  # Kitchen Management Service
  kitchen-service:
    build:
      context: .
      dockerfile: docker/Dockerfile.kitchen
    container_name: kitchen-service
    ports:
      - "50052:50052"
    environment:
      - GRPC_PORT=50052
      - REDIS_URL=redis://redis:6379
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - OLLAMA_BASE_URL=${OLLAMA_BASE_URL:-http://localhost:11434}
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "grpc_health_probe", "-addr=:50052"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - ai-order-network
    volumes:
      - ./logs:/app/logs

  # Communication Hub Service
  communication-hub:
    build:
      context: .
      dockerfile: docker/Dockerfile.communication
    container_name: communication-hub
    ports:
      - "50053:50053"
    environment:
      - GRPC_PORT=50053
      - REDIS_URL=redis://redis:6379
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - OLLAMA_BASE_URL=${OLLAMA_BASE_URL:-http://localhost:11434}
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "grpc_health_probe", "-addr=:50053"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - ai-order-network
    volumes:
      - ./logs:/app/logs

  # User Gateway Service (HTTP API)
  user-gateway:
    build:
      context: .
      dockerfile: docker/Dockerfile.gateway
    container_name: user-gateway
    ports:
      - "8080:8080"
    environment:
      - HTTP_PORT=8080
      - AI_ORDER_SERVICE_ADDR=ai-order-service:50051
      - KITCHEN_SERVICE_ADDR=kitchen-service:50052
      - COMMUNICATION_HUB_ADDR=communication-hub:50053
    depends_on:
      - ai-order-service
      - kitchen-service
      - communication-hub
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - ai-order-network
    volumes:
      - ./logs:/app/logs
      - ./web:/app/web

  # PostgreSQL for persistent data (optional)
  postgres:
    image: postgres:15-alpine
    container_name: postgres-ai-order
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=ai_order_db
      - POSTGRES_USER=ai_order_user
      - POSTGRES_PASSWORD=ai_order_pass
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-ai-order-db.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ai_order_user -d ai_order_db"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - ai-order-network

  # Prometheus for monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus-ai-order
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus-ai-order.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - ai-order-network

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    container_name: grafana-ai-order
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - ai-order-network

  # Jaeger for distributed tracing
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: jaeger-ai-order
    ports:
      - "16686:16686"
      - "14268:14268"
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    networks:
      - ai-order-network

  # NGINX for load balancing and reverse proxy
  nginx:
    image: nginx:alpine
    container_name: nginx-ai-order
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx-ai-order.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - user-gateway
    networks:
      - ai-order-network

  # Ollama for local AI inference (optional)
  ollama:
    image: ollama/ollama:latest
    container_name: ollama-ai-order
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0
    networks:
      - ai-order-network

  # Redis Insight for Redis management
  redis-insight:
    image: redislabs/redisinsight:latest
    container_name: redis-insight-ai-order
    ports:
      - "8001:8001"
    depends_on:
      - redis
    networks:
      - ai-order-network

volumes:
  redis_data:
    driver: local
  postgres_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  ollama_data:
    driver: local

networks:
  ai-order-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
