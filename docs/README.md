# Coffee Order System Documentation

Welcome to the Coffee Order System documentation. This documentation provides detailed information about the system architecture, components, API endpoints, and usage instructions.

## Table of Contents

### Core Documentation

1. [Overview](overview.md)
2. [Architecture](architecture.md)
3. [Installation](installation.md)
4. [Configuration](configuration.md)
5. [API Reference](api-reference.md)
6. [Kafka Integration](kafka-integration.md)
7. [Development Guide](development-guide.md)
8. [Testing](testing.md)
9. [Troubleshooting](troubleshooting.md)

### Additional Documentation

1. [Security](security.md)
2. [Performance](performance.md)
3. [Terraform](terraform.md)
4. [Docker and Kubernetes](docker-kubernetes.md)
5. [Roadmap](roadmap.md)
6. [Glossary](glossary.md)
7. [Frequently Asked Questions (FAQ)](faq.md)

### Diagrams

- [Architecture Diagram](images/architecture.md)
- [Sequence Diagram](images/sequence.md)
- [Component Diagram](images/component.md)
- [Deployment Diagram](images/deployment.md)

## Quick Start

For a quick start, check the [Installation](installation.md) document.

## Contributing

For information on how to contribute to this project, see the [Development Guide](development-guide.md).

## License

This project is licensed under the MIT License - see the LICENSE file for details.
