version: '3.8'

services:
  # Security Gateway Service
  security-gateway:
    build:
      context: .
      dockerfile: cmd/security-gateway/Dockerfile
    container_name: security-gateway
    ports:
      - "8080:8080"
      - "9090:9090"  # Metrics port
    environment:
      # Server Configuration
      - SERVER_PORT=8080
      - SERVER_HOST=0.0.0.0
      
      # Redis Configuration
      - REDIS_URL=redis://redis:6379
      - REDIS_DB=0
      - REDIS_PASSWORD=
      
      # Security Configuration
      - AES_KEY=${AES_KEY:-}
      - RSA_KEY=${RSA_KEY:-}
      - JWT_SECRET=${JWT_SECRET:-your-super-secret-jwt-key-change-in-production}
      
      # Logging Configuration
      - LOG_LEVEL=info
      - ENVIRONMENT=development
      
      # Service URLs
      - AUTH_SERVICE_URL=http://auth-service:8081
      - ORDER_SERVICE_URL=http://order-service:8082
      - PAYMENT_SERVICE_URL=http://payment-service:8083
      - USER_SERVICE_URL=http://user-service:8084
      
      # Monitoring
      - JAEGER_ENDPOINT=http://jaeger:14268/api/traces
      
    depends_on:
      - redis
      - jaeger
    networks:
      - security-gateway-network
    volumes:
      - ./cmd/security-gateway/config:/app/config:ro
      - ./logs:/var/log
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis for caching and rate limiting
  redis:
    image: redis:7-alpine
    container_name: security-gateway-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    volumes:
      - redis-data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf:ro
    networks:
      - security-gateway-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Jaeger for distributed tracing
  jaeger:
    image: jaegertracing/all-in-one:1.50
    container_name: security-gateway-jaeger
    ports:
      - "16686:16686"  # Jaeger UI
      - "14268:14268"  # HTTP collector
      - "14250:14250"  # gRPC collector
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    networks:
      - security-gateway-network
    restart: unless-stopped

  # Prometheus for metrics collection
  prometheus:
    image: prom/prometheus:v2.47.0
    container_name: security-gateway-prometheus
    ports:
      - "9090:9090"
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    networks:
      - security-gateway-network
    restart: unless-stopped

  # Grafana for metrics visualization
  grafana:
    image: grafana/grafana:10.1.0
    container_name: security-gateway-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards:ro
    networks:
      - security-gateway-network
    restart: unless-stopped
    depends_on:
      - prometheus

  # AlertManager for alerting
  alertmanager:
    image: prom/alertmanager:v0.26.0
    container_name: security-gateway-alertmanager
    ports:
      - "9093:9093"
    volumes:
      - ./monitoring/alertmanager/alertmanager.yml:/etc/alertmanager/alertmanager.yml:ro
      - alertmanager-data:/alertmanager
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
      - '--web.external-url=http://localhost:9093'
    networks:
      - security-gateway-network
    restart: unless-stopped

  # Nginx for load balancing and SSL termination
  nginx:
    image: nginx:1.25-alpine
    container_name: security-gateway-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./nginx/html:/usr/share/nginx/html:ro
    depends_on:
      - security-gateway
    networks:
      - security-gateway-network
    restart: unless-stopped

  # Mock backend services for testing
  auth-service-mock:
    image: mockserver/mockserver:5.15.0
    container_name: auth-service-mock
    ports:
      - "8081:1080"
    environment:
      - MOCKSERVER_PROPERTY_FILE=/config/mockserver.properties
    volumes:
      - ./test/mocks/auth-service:/config:ro
    networks:
      - security-gateway-network
    restart: unless-stopped

  order-service-mock:
    image: mockserver/mockserver:5.15.0
    container_name: order-service-mock
    ports:
      - "8082:1080"
    environment:
      - MOCKSERVER_PROPERTY_FILE=/config/mockserver.properties
    volumes:
      - ./test/mocks/order-service:/config:ro
    networks:
      - security-gateway-network
    restart: unless-stopped

  payment-service-mock:
    image: mockserver/mockserver:5.15.0
    container_name: payment-service-mock
    ports:
      - "8083:1080"
    environment:
      - MOCKSERVER_PROPERTY_FILE=/config/mockserver.properties
    volumes:
      - ./test/mocks/payment-service:/config:ro
    networks:
      - security-gateway-network
    restart: unless-stopped

  user-service-mock:
    image: mockserver/mockserver:5.15.0
    container_name: user-service-mock
    ports:
      - "8084:1080"
    environment:
      - MOCKSERVER_PROPERTY_FILE=/config/mockserver.properties
    volumes:
      - ./test/mocks/user-service:/config:ro
    networks:
      - security-gateway-network
    restart: unless-stopped

  # ELK Stack for log aggregation
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.10.0
    container_name: security-gateway-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data
    networks:
      - security-gateway-network
    restart: unless-stopped

  kibana:
    image: docker.elastic.co/kibana/kibana:8.10.0
    container_name: security-gateway-kibana
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
    networks:
      - security-gateway-network
    restart: unless-stopped

  logstash:
    image: docker.elastic.co/logstash/logstash:8.10.0
    container_name: security-gateway-logstash
    volumes:
      - ./monitoring/logstash/pipeline:/usr/share/logstash/pipeline:ro
      - ./monitoring/logstash/config:/usr/share/logstash/config:ro
    ports:
      - "5044:5044"
      - "9600:9600"
    environment:
      - "LS_JAVA_OPTS=-Xmx256m -Xms256m"
    depends_on:
      - elasticsearch
    networks:
      - security-gateway-network
    restart: unless-stopped

networks:
  security-gateway-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  redis-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
  alertmanager-data:
    driver: local
  elasticsearch-data:
    driver: local
