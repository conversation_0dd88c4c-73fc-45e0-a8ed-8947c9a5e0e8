version: '3.8'

services:
  redis8:
    image: redis/redis-stack:latest
    container_name: redis8-visual
    ports:
      - "6379:6379"
      - "8001:8001"  # RedisInsight
    volumes:
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf
      - redis8_data:/data
    environment:
      - REDIS_ARGS=--loadmodule /opt/redis-stack/lib/redisearch.so --loadmodule /opt/redis-stack/lib/rejson.so --loadmodule /opt/redis-stack/lib/redistimeseries.so --loadmodule /opt/redis-stack/lib/redisbloom.so --loadmodule /opt/redis-stack/lib/redisgraph.so
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - redis-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis-mcp-server:
    build:
      context: .
      dockerfile: Dockerfile.redis-mcp
    container_name: redis-mcp-server
    ports:
      - "8080:8080"
    environment:
      - REDIS_URL=redis://redis8:6379
      - PORT=8080
      - LOG_LEVEL=info
    depends_on:
      redis8:
        condition: service_healthy
    networks:
      - redis-network
    restart: unless-stopped
    volumes:
      - ./config:/app/config:ro

  web-ui:
    build:
      context: ./web-ui
      dockerfile: Dockerfile
    container_name: redis-web-ui
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8080
      - NEXT_PUBLIC_WS_URL=ws://localhost:8080
    depends_on:
      - redis-mcp-server
    networks:
      - redis-network
    restart: unless-stopped

volumes:
  redis8_data:
    driver: local

networks:
  redis-network:
    driver: bridge
