global:
  scrape_interval: 15s
  evaluation_interval: 15s
  scrape_timeout: 10s

alerting:
  alertmanagers:
    - static_configs:
        - targets: ['alertmanager:9093']

rule_files:
  - "rules/*.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'producer'
    static_configs:
      - targets: ['producer:3001']
    metrics_path: '/metrics'

  - job_name: 'consumer'
    static_configs:
      - targets: ['consumer:9091']
    metrics_path: '/metrics'

  - job_name: 'streams'
    static_configs:
      - targets: ['streams:9092']
    metrics_path: '/metrics'

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']

  - job_name: 'kafka'
    static_configs:
      - targets: ['kafka-exporter:9308']
