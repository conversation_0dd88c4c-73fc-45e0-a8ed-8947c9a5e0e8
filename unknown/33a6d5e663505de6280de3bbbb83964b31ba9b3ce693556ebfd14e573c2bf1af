apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: accounts-service
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/rewrite-target: /$1
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  tls:
  - hosts:
    - api.example.com
    secretName: accounts-service-tls
  rules:
  - host: api.example.com
    http:
      paths:
      - path: /accounts(/|$)(.*)
        pathType: Prefix
        backend:
          service:
            name: accounts-service
            port:
              name: http
