apiVersion: v2
name: fintech-platform
description: A comprehensive fintech platform with Web3 capabilities
type: application
version: 0.1.0
appVersion: "1.0.0"
home: https://github.com/Dima<PERSON>oyti/go-coffee
sources:
  - https://github.com/Di<PERSON><PERSON><PERSON>ti/go-coffee
maintainers:
  - name: <PERSON><PERSON><PERSON><PERSON><PERSON>
    email: <EMAIL>
keywords:
  - fintech
  - web3
  - blockchain
  - payments
  - trading
  - defi
  - api
dependencies:
  - name: postgresql
    version: "12.x.x"
    repository: https://charts.bitnami.com/bitnami
    condition: postgresql.enabled
  - name: redis
    version: "17.x.x"
    repository: https://charts.bitnami.com/bitnami
    condition: redis.enabled
  - name: prometheus
    version: "23.x.x"
    repository: https://prometheus-community.github.io/helm-charts
    condition: monitoring.prometheus.enabled
  - name: grafana
    version: "6.x.x"
    repository: https://grafana.github.io/helm-charts
    condition: monitoring.grafana.enabled
annotations:
  category: Financial Technology
  licenses: MIT
