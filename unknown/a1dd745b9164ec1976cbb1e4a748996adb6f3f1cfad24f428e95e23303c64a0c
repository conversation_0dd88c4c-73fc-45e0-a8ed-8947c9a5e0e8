apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-pvc
  namespace: fintech-platform
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi
  storageClassName: fast-ssd

---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: redis
  namespace: fintech-platform
  labels:
    app: redis
    component: cache
spec:
  serviceName: redis-service
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
        component: cache
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
          name: redis
        command:
        - redis-server
        - /etc/redis/redis.conf
        volumeMounts:
        - name: redis-storage
          mountPath: /data
        - name: redis-config
          mountPath: /etc/redis
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
      volumes:
      - name: redis-storage
        persistentVolumeClaim:
          claimName: redis-pvc
      - name: redis-config
        configMap:
          name: redis-config

---
apiVersion: v1
kind: Service
metadata:
  name: redis-service
  namespace: fintech-platform
  labels:
    app: redis
    component: cache
spec:
  type: ClusterIP
  ports:
  - port: 6379
    targetPort: 6379
    protocol: TCP
    name: redis
  selector:
    app: redis

---
# Redis Sentinel for High Availability (optional)
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis-sentinel
  namespace: fintech-platform
  labels:
    app: redis-sentinel
    component: cache
spec:
  replicas: 3
  selector:
    matchLabels:
      app: redis-sentinel
  template:
    metadata:
      labels:
        app: redis-sentinel
        component: cache
    spec:
      containers:
      - name: redis-sentinel
        image: redis:7-alpine
        ports:
        - containerPort: 26379
          name: sentinel
        command:
        - redis-sentinel
        - /etc/redis-sentinel/sentinel.conf
        volumeMounts:
        - name: sentinel-config
          mountPath: /etc/redis-sentinel
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"
        livenessProbe:
          exec:
            command:
            - redis-cli
            - -p
            - "26379"
            - ping
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - redis-cli
            - -p
            - "26379"
            - ping
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: sentinel-config
        configMap:
          name: redis-sentinel-config

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: redis-sentinel-config
  namespace: fintech-platform
data:
  sentinel.conf: |
    port 26379
    bind 0.0.0.0
    
    sentinel monitor mymaster redis-service 6379 2
    sentinel down-after-milliseconds mymaster 5000
    sentinel parallel-syncs mymaster 1
    sentinel failover-timeout mymaster 10000
    
    sentinel deny-scripts-reconfig yes
    
    logfile ""
    loglevel notice

---
apiVersion: v1
kind: Service
metadata:
  name: redis-sentinel-service
  namespace: fintech-platform
  labels:
    app: redis-sentinel
    component: cache
spec:
  type: ClusterIP
  ports:
  - port: 26379
    targetPort: 26379
    protocol: TCP
    name: sentinel
  selector:
    app: redis-sentinel

---
# Redis Backup CronJob
apiVersion: batch/v1
kind: CronJob
metadata:
  name: redis-backup
  namespace: fintech-platform
spec:
  schedule: "0 3 * * *"  # Daily at 3 AM
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: redis-backup
            image: redis:7-alpine
            command:
            - /bin/sh
            - -c
            - |
              BACKUP_FILE="/backup/redis_backup_$(date +%Y%m%d_%H%M%S).rdb"
              redis-cli -h redis-service --rdb $BACKUP_FILE
              echo "Redis backup completed: $BACKUP_FILE"
              # Upload to cloud storage (implement based on your provider)
            volumeMounts:
            - name: backup-storage
              mountPath: /backup
          volumes:
          - name: backup-storage
            persistentVolumeClaim:
              claimName: redis-backup-pvc
          restartPolicy: OnFailure

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-backup-pvc
  namespace: fintech-platform
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: standard

---
# Redis Monitoring Service
apiVersion: v1
kind: Service
metadata:
  name: redis-exporter-service
  namespace: fintech-platform
  labels:
    app: redis-exporter
    component: monitoring
spec:
  type: ClusterIP
  ports:
  - port: 9121
    targetPort: 9121
    protocol: TCP
    name: metrics
  selector:
    app: redis-exporter

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis-exporter
  namespace: fintech-platform
  labels:
    app: redis-exporter
    component: monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis-exporter
  template:
    metadata:
      labels:
        app: redis-exporter
        component: monitoring
    spec:
      containers:
      - name: redis-exporter
        image: oliver006/redis_exporter:latest
        ports:
        - containerPort: 9121
          name: metrics
        env:
        - name: REDIS_ADDR
          value: "redis://redis-service:6379"
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
        livenessProbe:
          httpGet:
            path: /metrics
            port: 9121
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /metrics
            port: 9121
          initialDelaySeconds: 5
          periodSeconds: 5
