{"name": "crypto-terminal-frontend", "version": "1.0.0", "description": "Crypto Market Terminal Frontend", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "react-scripts": "5.0.1", "typescript": "^4.9.0", "web-vitals": "^2.1.4", "axios": "^1.3.0", "recharts": "^2.5.0", "socket.io-client": "^4.6.0", "tailwindcss": "^3.2.0", "@headlessui/react": "^1.7.0", "@heroicons/react": "^2.0.0", "clsx": "^1.2.0", "date-fns": "^2.29.0", "react-hot-toast": "^2.4.0", "zustand": "^4.3.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write src/**/*.{ts,tsx,css,md}"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/socket.io-client": "^3.0.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "prettier": "^2.8.0", "eslint": "^8.0.0", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0"}, "proxy": "http://localhost:8090"}