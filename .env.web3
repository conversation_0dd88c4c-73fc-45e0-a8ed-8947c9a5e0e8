# =============================================================================
# WEB3 & BLOCKCHAIN SERVICES - ENVIRONMENT CONFIGURATION
# =============================================================================
# Specialized configuration for Web3 wallet and DeFi services
# =============================================================================

# =============================================================================
# WEB3 SERVICES CONFIGURATION
# =============================================================================

# Service Ports
WEB3_API_GATEWAY_PORT=8081
WEB3_WALLET_SERVICE_PORT=8083
WEB3_DEFI_SERVICE_PORT=8082
WEB3_TRANSACTION_SERVICE_PORT=8084
WEB3_SMART_CONTRACT_SERVICE_PORT=8085

# Service Settings
WEB3_ENVIRONMENT=development
WEB3_DEBUG=true
WEB3_LOG_LEVEL=info

# =============================================================================
# ETHEREUM CONFIGURATION
# =============================================================================

# Ethereum Networks
ETHEREUM_MAINNET_RPC_URL=https://mainnet.infura.io/v3/your-project-id
ETHEREUM_GOERLI_RPC_URL=https://goerli.infura.io/v3/your-project-id
ETHEREUM_SEPOLIA_RPC_URL=https://sepolia.infura.io/v3/your-project-id
ETHEREUM_POLYGON_RPC_URL=https://polygon-mainnet.infura.io/v3/your-project-id
ETHEREUM_BSC_RPC_URL=https://bsc-dataseed.binance.org

# Ethereum Configuration
ETHEREUM_NETWORK=goerli
ETHEREUM_CHAIN_ID=5
ETHEREUM_PRIVATE_KEY=your-ethereum-private-key
ETHEREUM_PUBLIC_KEY=your-ethereum-public-key
ETHEREUM_WALLET_ADDRESS=your-ethereum-wallet-address

# Gas Configuration
ETHEREUM_GAS_LIMIT=21000
ETHEREUM_GAS_PRICE=20000000000
ETHEREUM_MAX_FEE_PER_GAS=30000000000
ETHEREUM_MAX_PRIORITY_FEE_PER_GAS=2000000000
ETHEREUM_GAS_MULTIPLIER=1.2

# =============================================================================
# BITCOIN CONFIGURATION
# =============================================================================

# Bitcoin Networks
BITCOIN_MAINNET_RPC_URL=https://your-bitcoin-mainnet-node.com
BITCOIN_TESTNET_RPC_URL=https://your-bitcoin-testnet-node.com
BITCOIN_RPC_USERNAME=your-bitcoin-rpc-username
BITCOIN_RPC_PASSWORD=your-bitcoin-rpc-password

# Bitcoin Configuration
BITCOIN_NETWORK=testnet
BITCOIN_PRIVATE_KEY=your-bitcoin-private-key
BITCOIN_PUBLIC_KEY=your-bitcoin-public-key
BITCOIN_WALLET_ADDRESS=your-bitcoin-wallet-address

# Bitcoin Transaction Settings
BITCOIN_FEE_RATE=10
BITCOIN_CONFIRMATION_BLOCKS=6
BITCOIN_DUST_THRESHOLD=546

# =============================================================================
# SOLANA CONFIGURATION
# =============================================================================

# Solana Networks
SOLANA_MAINNET_RPC_URL=https://api.mainnet-beta.solana.com
SOLANA_TESTNET_RPC_URL=https://api.testnet.solana.com
SOLANA_DEVNET_RPC_URL=https://api.devnet.solana.com

# Solana Configuration
SOLANA_NETWORK=devnet
SOLANA_PRIVATE_KEY=your-solana-private-key
SOLANA_PUBLIC_KEY=your-solana-public-key
SOLANA_WALLET_ADDRESS=your-solana-wallet-address

# Solana Transaction Settings
SOLANA_COMMITMENT=confirmed
SOLANA_SKIP_PREFLIGHT=false
SOLANA_MAX_RETRIES=3

# =============================================================================
# DEFI PROTOCOLS CONFIGURATION
# =============================================================================

# Uniswap V3
UNISWAP_V3_ROUTER=******************************************
UNISWAP_V3_FACTORY=******************************************
UNISWAP_V3_QUOTER=******************************************
UNISWAP_V3_POOL_FEE=3000

# Aave V3
AAVE_V3_LENDING_POOL=******************************************
AAVE_V3_DATA_PROVIDER=******************************************
AAVE_V3_ORACLE=******************************************

# Compound V3
COMPOUND_V3_COMPTROLLER=******************************************
COMPOUND_V3_USDC=******************************************

# 1inch
ONEINCH_API_URL=https://api.1inch.io/v5.0/1
ONEINCH_API_KEY=your-1inch-api-key

# Raydium (Solana)
RAYDIUM_PROGRAM_ID=675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8
RAYDIUM_AMM_PROGRAM_ID=675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8

# Jupiter (Solana)
JUPITER_API_URL=https://quote-api.jup.ag/v6
JUPITER_PROGRAM_ID=JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4

# =============================================================================
# SMART CONTRACTS
# =============================================================================

# Coffee Token Contract
COFFEE_TOKEN_ADDRESS=******************************************
COFFEE_TOKEN_DECIMALS=18
COFFEE_TOKEN_SYMBOL=COFFEE
COFFEE_TOKEN_NAME=Go Coffee Token

# Staking Contract
COFFEE_STAKING_CONTRACT=******************************************
COFFEE_STAKING_REWARD_RATE=100
COFFEE_STAKING_LOCK_PERIOD=2592000

# NFT Contract
COFFEE_NFT_CONTRACT=******************************************
COFFEE_NFT_BASE_URI=https://api.gocoffee.com/nft/metadata/

# =============================================================================
# WALLET MANAGEMENT
# =============================================================================

# Wallet Security
WALLET_ENCRYPTION_KEY=your-32-character-wallet-encryption-key
WALLET_BACKUP_ENABLED=true
WALLET_BACKUP_INTERVAL=3600s

# Multi-Signature
MULTISIG_ENABLED=false
MULTISIG_THRESHOLD=2
MULTISIG_SIGNERS=3

# Hardware Wallet Support
LEDGER_SUPPORT_ENABLED=false
TREZOR_SUPPORT_ENABLED=false

# =============================================================================
# DEFI TRADING STRATEGIES
# =============================================================================

# Arbitrage Bot
ARBITRAGE_ENABLED=true
ARBITRAGE_MIN_PROFIT_THRESHOLD=0.01
ARBITRAGE_MAX_SLIPPAGE=0.005
ARBITRAGE_GAS_LIMIT_MULTIPLIER=1.5

# Yield Farming
YIELD_FARMING_ENABLED=true
YIELD_FARMING_AUTO_COMPOUND=true
YIELD_FARMING_MIN_APY=0.08
YIELD_FARMING_MAX_RISK_SCORE=7

# Grid Trading
GRID_TRADING_ENABLED=true
GRID_TRADING_GRID_SIZE=10
GRID_TRADING_PRICE_RANGE=0.1
GRID_TRADING_REBALANCE_INTERVAL=3600s

# DCA (Dollar Cost Averaging)
DCA_ENABLED=true
DCA_INTERVAL=86400s
DCA_AMOUNT=100
DCA_TOKENS=ETH,BTC,USDC

# =============================================================================
# PRICE FEEDS & ORACLES
# =============================================================================

# Chainlink Oracles
CHAINLINK_ETH_USD_FEED=******************************************
CHAINLINK_BTC_USD_FEED=******************************************
CHAINLINK_USDC_USD_FEED=******************************************

# Price Update Settings
PRICE_UPDATE_INTERVAL=30s
PRICE_DEVIATION_THRESHOLD=0.01
PRICE_STALENESS_THRESHOLD=300s

# =============================================================================
# TRANSACTION MANAGEMENT
# =============================================================================

# Transaction Settings
TX_CONFIRMATION_BLOCKS=12
TX_TIMEOUT=300s
TX_RETRY_ATTEMPTS=3
TX_RETRY_DELAY=10s

# MEV Protection
MEV_PROTECTION_ENABLED=true
FLASHBOTS_RELAY_URL=https://relay.flashbots.net
PRIVATE_MEMPOOL_ENABLED=false

# =============================================================================
# SECURITY & COMPLIANCE
# =============================================================================

# Security Settings
ENABLE_TRANSACTION_SIGNING=true
ENABLE_ADDRESS_VALIDATION=true
ENABLE_AMOUNT_VALIDATION=true
MAX_TRANSACTION_AMOUNT=10000

# Compliance
KYC_ENABLED=false
AML_ENABLED=false
SANCTIONS_CHECK_ENABLED=false

# Risk Management
DAILY_TRANSACTION_LIMIT=50000
MONTHLY_TRANSACTION_LIMIT=1000000
SUSPICIOUS_ACTIVITY_THRESHOLD=10000

# =============================================================================
# MONITORING & ALERTS
# =============================================================================

# Blockchain Monitoring
MONITOR_BLOCK_CONFIRMATIONS=true
MONITOR_GAS_PRICES=true
MONITOR_NETWORK_CONGESTION=true

# Alert Thresholds
HIGH_GAS_PRICE_THRESHOLD=100000000000
LOW_BALANCE_THRESHOLD=0.1
FAILED_TRANSACTION_THRESHOLD=5

# =============================================================================
# DEVELOPMENT & TESTING
# =============================================================================

# Development Settings
WEB3_ENABLE_TESTNET=true
WEB3_MOCK_TRANSACTIONS=false
WEB3_ENABLE_FAUCET=true

# Testing Configuration
TEST_PRIVATE_KEY=your-test-private-key
TEST_WALLET_ADDRESS=your-test-wallet-address
TEST_TOKEN_AMOUNT=1000000000000000000

# Simulation
ENABLE_TRANSACTION_SIMULATION=true
SIMULATION_PROVIDER=tenderly
