name: Accounts Service CI/CD

on:
  push:
    branches: [ main ]
    paths:
      - 'accounts-service/**'
      - '.github/workflows/accounts-service.yml'
  pull_request:
    branches: [ main ]
    paths:
      - 'accounts-service/**'
      - '.github/workflows/accounts-service.yml'

env:
  REGISTRY: gcr.io
  PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}
  SERVICE: accounts-service
  REGION: us-central1

jobs:
  test:
    name: Test
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./accounts-service

    services:
      postgres:
        image: postgres:16
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: coffee_accounts_test
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: '1.22'
          cache: true
          cache-dependency-path: accounts-service/go.sum

      - name: Install dependencies
        run: go mod download

      - name: Run unit tests
        run: go test -v ./... -short

      - name: Run integration tests
        run: go test -v ./... -tags=integration
        env:
          DB_HOST: localhost
          DB_PORT: 5432
          DB_USER: postgres
          DB_PASSWORD: postgres
          DB_NAME: coffee_accounts_test
          DB_SSLMODE: disable

  build:
    name: Build and Push
    needs: test
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    defaults:
      run:
        working-directory: ./accounts-service

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2
        with:
          project_id: ${{ env.PROJECT_ID }}

      - name: Configure Docker to use gcloud as a credential helper
        run: gcloud auth configure-docker

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: ./accounts-service
          push: true
          tags: ${{ env.REGISTRY }}/${{ env.PROJECT_ID }}/${{ env.SERVICE }}:${{ github.sha }},${{ env.REGISTRY }}/${{ env.PROJECT_ID }}/${{ env.SERVICE }}:latest
          cache-from: type=gha
          cache-to: type=gha,mode=max

  deploy:
    name: Deploy
    needs: build
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2
        with:
          project_id: ${{ env.PROJECT_ID }}

      - name: Get GKE credentials
        uses: google-github-actions/get-gke-credentials@v2
        with:
          cluster_name: coffee-order-system-gke
          location: ${{ env.REGION }}

      - name: Set up Kustomize
        run: |
          curl -s "https://raw.githubusercontent.com/kubernetes-sigs/kustomize/master/hack/install_kustomize.sh" | bash
          sudo mv kustomize /usr/local/bin/

      - name: Deploy to GKE
        run: |
          cd kubernetes/accounts-service
          kustomize edit set image ${REGISTRY}/${PROJECT_ID}/${SERVICE}=${REGISTRY}/${PROJECT_ID}/${SERVICE}:${GITHUB_SHA}
          kustomize build . | kubectl apply -f -
          kubectl rollout status deployment/${SERVICE} --timeout=5m
