# Multi-stage build for Redis MCP Server
FROM golang:1.22-alpine AS builder

# Set working directory
WORKDIR /app

# Install dependencies
RUN apk add --no-cache git ca-certificates tzdata

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Build the Redis MCP server
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o redis-mcp-server ./cmd/redis-mcp-server/main.go

# Final stage
FROM alpine:latest

# Install ca-certificates for HTTPS requests
RUN apk --no-cache add ca-certificates curl

WORKDIR /root/

# Copy the binary from builder stage
COPY --from=builder /app/redis-mcp-server .

# Copy configuration files
COPY --from=builder /app/config/ ./config/

# Expose port
EXPOSE 8090

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8090/api/v1/redis-mcp/health || exit 1

# Run the binary
CMD ["./redis-mcp-server"]
