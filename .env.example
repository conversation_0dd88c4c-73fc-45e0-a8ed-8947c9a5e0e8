# =============================================================================
# GO COFFEE - ENVIRONMENT CONFIGURATION TEMPLATE
# =============================================================================
# Copy this file to .env and update with your actual values
# This file serves as a template and documentation for all environment variables
# =============================================================================

# =============================================================================
# CORE APPLICATION SETTINGS
# =============================================================================

# Environment (development, staging, production)
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=info
LOG_FORMAT=json

# Application Info
APP_NAME=go-coffee
APP_VERSION=1.0.0
APP_DESCRIPTION="Revolutionary Web3 Coffee Ecosystem"

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================

# API Gateway
API_GATEWAY_PORT=8080
API_GATEWAY_HOST=0.0.0.0

# Producer Service
PRODUCER_PORT=3000
PRODUCER_HOST=0.0.0.0

# Consumer Service  
CONSUMER_PORT=3001
CONSUMER_HOST=0.0.0.0

# Streams Service
STREAMS_PORT=3002
STREAMS_HOST=0.0.0.0

# AI Search Engine
AI_SEARCH_PORT=8092
AI_SEARCH_HOST=0.0.0.0

# Auth Service
AUTH_SERVICE_PORT=8091
AUTH_SERVICE_HOST=0.0.0.0

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# PostgreSQL
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=go_coffee
DATABASE_USER=postgres
DATABASE_PASSWORD=your-database-password
DATABASE_SSL_MODE=disable
DATABASE_MAX_OPEN_CONNS=25
DATABASE_MAX_IDLE_CONNS=5
DATABASE_CONN_MAX_LIFETIME=300s

# Test Database
TEST_DATABASE_NAME=go_coffee_test

# =============================================================================
# CACHE & MESSAGE QUEUE
# =============================================================================

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password
REDIS_DB=0
REDIS_POOL_SIZE=10
REDIS_MIN_IDLE_CONNS=5
REDIS_DIAL_TIMEOUT=5s
REDIS_READ_TIMEOUT=3s
REDIS_WRITE_TIMEOUT=3s
REDIS_URL=redis://localhost:6379

# Kafka Configuration
KAFKA_BROKERS=localhost:9092
KAFKA_TOPIC=coffee_orders
KAFKA_PROCESSED_TOPIC=processed_orders
KAFKA_CONSUMER_GROUP=coffee-consumer-group
KAFKA_WORKER_POOL_SIZE=3
KAFKA_RETRY_MAX=5
KAFKA_REQUIRED_ACKS=all

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRY=24h
REFRESH_TOKEN_EXPIRY=720h
JWT_ISSUER=go-coffee
JWT_AUDIENCE=go-coffee-users

# API Keys
API_KEY_SECRET=your-api-key-secret-change-this
WEBHOOK_SECRET=your-webhook-secret-key-change-this

# Encryption
ENCRYPTION_KEY=your-32-character-encryption-key!!

# =============================================================================
# WEB3 & BLOCKCHAIN CONFIGURATION
# =============================================================================

# Ethereum
ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/your-project-id
ETHEREUM_TESTNET_RPC_URL=https://goerli.infura.io/v3/your-project-id
ETHEREUM_PRIVATE_KEY=your-ethereum-private-key
ETHEREUM_GAS_LIMIT=21000
ETHEREUM_GAS_PRICE=20000000000

# Bitcoin
BITCOIN_RPC_URL=https://your-bitcoin-node.com
BITCOIN_RPC_USERNAME=your-bitcoin-rpc-username
BITCOIN_RPC_PASSWORD=your-bitcoin-rpc-password

# Solana
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
SOLANA_TESTNET_RPC_URL=https://api.testnet.solana.com
SOLANA_PRIVATE_KEY=your-solana-private-key

# DeFi Protocols
UNISWAP_V3_ROUTER=******************************************
AAVE_LENDING_POOL=******************************************
COMPOUND_COMPTROLLER=******************************************

# =============================================================================
# AI & MACHINE LEARNING
# =============================================================================

# AI Providers
GEMINI_API_KEY=your-gemini-api-key
OPENAI_API_KEY=your-openai-api-key
OLLAMA_URL=http://localhost:11434

# AI Search Configuration
AI_SEARCH_EMBEDDING_MODEL=coffee_ai_v2
AI_SEARCH_VECTOR_DIMENSIONS=384
AI_SEARCH_SIMILARITY_THRESHOLD=0.7
AI_SEARCH_MAX_RESULTS=50

# =============================================================================
# EXTERNAL INTEGRATIONS
# =============================================================================

# Email (SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=Go Coffee

# SMS (Twilio)
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_FROM_NUMBER=+**********

# Slack Integration
SLACK_BOT_TOKEN=xoxb-your-slack-bot-token
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your/webhook/url

# ClickUp Integration
CLICKUP_API_TOKEN=your-clickup-api-token
CLICKUP_TEAM_ID=your-clickup-team-id

# Google Sheets
GOOGLE_SHEETS_CREDENTIALS_PATH=./credentials/google-sheets.json
GOOGLE_SHEETS_SPREADSHEET_ID=your-spreadsheet-id

# =============================================================================
# MONITORING & OBSERVABILITY
# =============================================================================

# Prometheus
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=9090
PROMETHEUS_METRICS_PATH=/metrics

# Grafana
GRAFANA_PORT=3000
GRAFANA_ADMIN_USER=admin
GRAFANA_ADMIN_PASSWORD=your-grafana-password

# Jaeger Tracing
JAEGER_ENABLED=true
JAEGER_ENDPOINT=http://localhost:14268/api/traces
JAEGER_SAMPLER_TYPE=const
JAEGER_SAMPLER_PARAM=1

# Sentry Error Tracking
SENTRY_DSN=your-sentry-dsn
SENTRY_ENVIRONMENT=development
SENTRY_RELEASE=1.0.0

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Core Services
PRODUCER_SERVICE_ENABLED=true
CONSUMER_SERVICE_ENABLED=true
STREAMS_SERVICE_ENABLED=true
API_GATEWAY_ENABLED=true

# Web3 Services
WEB3_WALLET_ENABLED=true
DEFI_SERVICE_ENABLED=true
SMART_CONTRACT_SERVICE_ENABLED=true

# AI Services
AI_SEARCH_ENABLED=true
AI_AGENTS_ENABLED=true

# Modules
AUTH_MODULE_ENABLED=true
PAYMENT_MODULE_ENABLED=true
NOTIFICATION_MODULE_ENABLED=true

# =============================================================================
# DEVELOPMENT & TESTING
# =============================================================================

# Development Settings
ENABLE_CORS=true
ENABLE_SWAGGER=true
ENABLE_DEBUG_ENDPOINTS=true
ENABLE_PROFILING=false

# Testing
RUN_INTEGRATION_TESTS=false
TEST_TIMEOUT=30s
ENABLE_TEST_ENDPOINTS=false

# Hot Reload
ENABLE_HOT_RELOAD=true
WATCH_DIRECTORIES=./cmd,./internal,./pkg

# =============================================================================
# DOCKER & KUBERNETES
# =============================================================================

# Docker Configuration
DOCKER_REGISTRY=ghcr.io/dimajoyti
DOCKER_TAG=latest
DOCKER_BUILD_TARGET=production

# Kubernetes
K8S_NAMESPACE=go-coffee
K8S_CLUSTER_NAME=go-coffee-cluster
K8S_CONTEXT=go-coffee-context

# =============================================================================
# BACKUP & STORAGE
# =============================================================================

# AWS S3 (for backups and file storage)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=go-coffee-storage

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_TYPE=s3

# =============================================================================
# INSTRUCTIONS FOR SETUP
# =============================================================================

# 1. Copy this file to .env:
#    cp .env.example .env

# 2. Update all placeholder values with your actual configuration

# 3. For production, copy to .env.production and update with production values:
#    cp .env.example .env.production

# 4. Ensure sensitive values are properly secured and never committed to version control

# 5. Use different values for each environment (development, staging, production)

# =============================================================================
# SECURITY NOTES
# =============================================================================

# - Never commit .env files with real secrets to version control
# - Use strong, unique passwords and API keys
# - Rotate secrets regularly
# - Use environment-specific configurations
# - Enable proper access controls and monitoring
# - Consider using secret management services for production
