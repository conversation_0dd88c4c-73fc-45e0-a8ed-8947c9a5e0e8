# =============================================================================
# AI SEARCH ENGINE - ENVIRONMENT CONFIGURATION
# =============================================================================
# Specialized configuration for Redis 8 AI Search Engine
# =============================================================================

# =============================================================================
# AI SEARCH SERVICE CONFIGURATION
# =============================================================================

# Service Settings
AI_SEARCH_SERVICE_NAME=redis8-ai-search-engine
AI_SEARCH_VERSION=2.0.0
AI_SEARCH_PORT=8092
AI_SEARCH_HOST=0.0.0.0
AI_SEARCH_ENVIRONMENT=development

# Performance Settings
AI_SEARCH_MAX_CONCURRENT_REQUESTS=1000
AI_SEARCH_REQUEST_TIMEOUT=30s
AI_SEARCH_RATE_LIMIT_REQUESTS_PER_MINUTE=1000
AI_SEARCH_CACHE_TTL=300s

# =============================================================================
# REDIS 8 CONFIGURATION
# =============================================================================

# Redis Connection
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_USERNAME=

# Redis Pool Settings
REDIS_POOL_SIZE=20
REDIS_MIN_IDLE_CONNS=10
REDIS_MAX_IDLE_CONNS=30
REDIS_DIAL_TIMEOUT=10s
REDIS_READ_TIMEOUT=5s
REDIS_WRITE_TIMEOUT=5s
REDIS_POOL_TIMEOUT=10s
REDIS_IDLE_TIMEOUT=300s

# Redis 8 AI Features
REDIS_VECTOR_SEARCH_ENABLED=true
REDIS_SEARCH_MODULE_ENABLED=true
REDIS_JSON_MODULE_ENABLED=true
REDIS_TIMESERIES_MODULE_ENABLED=true

# =============================================================================
# AI & MACHINE LEARNING CONFIGURATION
# =============================================================================

# Embedding Models
AI_SEARCH_EMBEDDING_MODEL=coffee_ai_v2
AI_SEARCH_VECTOR_DIMENSIONS=384
AI_SEARCH_EMBEDDING_PROVIDER=openai
AI_SEARCH_FALLBACK_EMBEDDING_PROVIDER=ollama

# Search Algorithms
AI_SEARCH_SIMILARITY_FUNCTION=cosine
AI_SEARCH_SIMILARITY_THRESHOLD=0.7
AI_SEARCH_MIN_SCORE=0.5
AI_SEARCH_MAX_RESULTS=50
AI_SEARCH_DEFAULT_LIMIT=10

# Hybrid Search Configuration
AI_SEARCH_SEMANTIC_WEIGHT=0.7
AI_SEARCH_KEYWORD_WEIGHT=0.3
AI_SEARCH_ENABLE_RERANKING=true
AI_SEARCH_FUSION_ALGORITHM=reciprocal_rank_fusion

# =============================================================================
# AI PROVIDERS CONFIGURATION
# =============================================================================

# OpenAI
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=text-embedding-ada-002
OPENAI_MAX_TOKENS=8192
OPENAI_TIMEOUT=30s
OPENAI_RETRY_ATTEMPTS=3

# Google Gemini
GEMINI_API_KEY=your-gemini-api-key
GEMINI_MODEL=gemini-pro
GEMINI_TIMEOUT=30s
GEMINI_RETRY_ATTEMPTS=3

# Ollama (Local AI)
OLLAMA_URL=http://localhost:11434
OLLAMA_MODEL=llama2
OLLAMA_TIMEOUT=60s
OLLAMA_RETRY_ATTEMPTS=2

# =============================================================================
# SEARCH INDEX CONFIGURATION
# =============================================================================

# Index Settings
AI_SEARCH_INDEX_NAME=coffee_items_index
AI_SEARCH_INDEX_PREFIX=coffee:
AI_SEARCH_AUTO_INDEX=true
AI_SEARCH_INDEX_REBUILD_INTERVAL=24h

# Document Types
AI_SEARCH_DOCUMENT_TYPES=coffee,tea,pastries,snacks,merchandise
AI_SEARCH_DEFAULT_CATEGORY=coffee

# Field Mappings
AI_SEARCH_TITLE_FIELD=title
AI_SEARCH_DESCRIPTION_FIELD=description
AI_SEARCH_CATEGORY_FIELD=category
AI_SEARCH_PRICE_FIELD=price
AI_SEARCH_VECTOR_FIELD=embedding

# =============================================================================
# CACHING & PERFORMANCE
# =============================================================================

# Query Caching
AI_SEARCH_ENABLE_QUERY_CACHE=true
AI_SEARCH_QUERY_CACHE_TTL=600s
AI_SEARCH_QUERY_CACHE_SIZE=10000

# Result Caching
AI_SEARCH_ENABLE_RESULT_CACHE=true
AI_SEARCH_RESULT_CACHE_TTL=300s
AI_SEARCH_RESULT_CACHE_SIZE=5000

# Precomputed Embeddings
AI_SEARCH_PRECOMPUTE_EMBEDDINGS=true
AI_SEARCH_EMBEDDING_CACHE_TTL=3600s

# =============================================================================
# ANALYTICS & MONITORING
# =============================================================================

# Search Analytics
AI_SEARCH_ENABLE_ANALYTICS=true
AI_SEARCH_TRACK_QUERIES=true
AI_SEARCH_TRACK_RESULTS=true
AI_SEARCH_TRACK_USER_BEHAVIOR=true

# Popular Queries Tracking
AI_SEARCH_POPULAR_QUERIES_LIMIT=100
AI_SEARCH_POPULAR_QUERIES_TTL=86400s

# Performance Monitoring
AI_SEARCH_ENABLE_METRICS=true
AI_SEARCH_METRICS_INTERVAL=60s
AI_SEARCH_SLOW_QUERY_THRESHOLD=1000ms

# =============================================================================
# PERSONALIZATION
# =============================================================================

# User Personalization
AI_SEARCH_ENABLE_PERSONALIZATION=true
AI_SEARCH_USER_PROFILE_TTL=2592000s
AI_SEARCH_PERSONALIZATION_WEIGHT=0.2

# Recommendation Engine
AI_SEARCH_ENABLE_RECOMMENDATIONS=true
AI_SEARCH_RECOMMENDATION_ALGORITHM=collaborative_filtering
AI_SEARCH_MIN_INTERACTIONS_FOR_RECOMMENDATIONS=5

# =============================================================================
# SECURITY & RATE LIMITING
# =============================================================================

# API Security
AI_SEARCH_ENABLE_API_KEY_AUTH=false
AI_SEARCH_API_KEY_HEADER=X-API-Key
AI_SEARCH_ENABLE_CORS=true
AI_SEARCH_CORS_ORIGINS=*

# Rate Limiting
AI_SEARCH_ENABLE_RATE_LIMITING=true
AI_SEARCH_RATE_LIMIT_REQUESTS_PER_MINUTE=1000
AI_SEARCH_RATE_LIMIT_BURST=100
AI_SEARCH_RATE_LIMIT_WINDOW=60s

# Request Validation
AI_SEARCH_MAX_QUERY_LENGTH=500
AI_SEARCH_MAX_FILTERS=10
AI_SEARCH_MAX_CATEGORIES=5

# =============================================================================
# LOGGING & DEBUGGING
# =============================================================================

# Logging Configuration
AI_SEARCH_LOG_LEVEL=info
AI_SEARCH_LOG_FORMAT=json
AI_SEARCH_ENABLE_REQUEST_LOGGING=true
AI_SEARCH_ENABLE_PERFORMANCE_LOGGING=true

# Debug Settings
AI_SEARCH_DEBUG_MODE=false
AI_SEARCH_ENABLE_QUERY_EXPLANATION=false
AI_SEARCH_LOG_EMBEDDINGS=false

# =============================================================================
# HEALTH CHECKS & MONITORING
# =============================================================================

# Health Check Configuration
AI_SEARCH_HEALTH_CHECK_INTERVAL=30s
AI_SEARCH_HEALTH_CHECK_TIMEOUT=5s
AI_SEARCH_REDIS_HEALTH_CHECK=true

# Metrics Export
AI_SEARCH_ENABLE_PROMETHEUS_METRICS=true
AI_SEARCH_PROMETHEUS_PORT=9092
AI_SEARCH_METRICS_PATH=/metrics

# =============================================================================
# DEVELOPMENT & TESTING
# =============================================================================

# Development Settings
AI_SEARCH_ENABLE_SWAGGER=true
AI_SEARCH_SWAGGER_PATH=/swagger
AI_SEARCH_ENABLE_DEBUG_ENDPOINTS=true

# Testing Configuration
AI_SEARCH_ENABLE_TEST_DATA=true
AI_SEARCH_TEST_DATA_SIZE=1000
AI_SEARCH_MOCK_AI_RESPONSES=false

# Hot Reload
AI_SEARCH_ENABLE_HOT_RELOAD=true
AI_SEARCH_WATCH_CONFIG_CHANGES=true
