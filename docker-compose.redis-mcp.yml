version: '3.8'

services:
  # Redis server for data storage
  redis:
    image: redis:7-alpine
    container_name: go-coffee-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - go-coffee-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis MCP Server
  redis-mcp-server:
    build:
      context: .
      dockerfile: Dockerfile.redis-mcp
    container_name: go-coffee-redis-mcp
    ports:
      - "8090:8090"
    environment:
      - REDIS_URL=redis://redis:6379
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - OLLAMA_BASE_URL=http://ollama:11434
      - LOG_LEVEL=info
      - SERVER_PORT=8090
    depends_on:
      redis:
        condition: service_healthy
      ollama:
        condition: service_started
    networks:
      - go-coffee-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8090/api/v1/redis-mcp/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Ollama for local LLM support
  ollama:
    image: ollama/ollama:latest
    container_name: go-coffee-ollama
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    networks:
      - go-coffee-network
    environment:
      - OLLAMA_HOST=0.0.0.0
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/version"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Enhanced Inventory Manager Agent
  inventory-manager-enhanced:
    build:
      context: .
      dockerfile: Dockerfile.inventory-agent
    container_name: go-coffee-inventory-enhanced
    environment:
      - REDIS_MCP_SERVER_URL=http://redis-mcp-server:8090
      - KAFKA_BROKER=kafka:9092
      - LOG_LEVEL=info
    depends_on:
      redis-mcp-server:
        condition: service_healthy
      kafka:
        condition: service_started
    networks:
      - go-coffee-network
    volumes:
      - ./ai-agents/inventory-manager-agent/config_enhanced.yaml:/app/config.yaml

  # Beverage Inventor Agent with Redis MCP
  beverage-inventor-enhanced:
    build:
      context: .
      dockerfile: Dockerfile.beverage-agent
    container_name: go-coffee-beverage-enhanced
    environment:
      - REDIS_MCP_SERVER_URL=http://redis-mcp-server:8090
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - KAFKA_BROKER=kafka:9092
    depends_on:
      redis-mcp-server:
        condition: service_healthy
    networks:
      - go-coffee-network

  # Task Manager Agent with Redis MCP
  task-manager-enhanced:
    build:
      context: .
      dockerfile: Dockerfile.task-agent
    container_name: go-coffee-task-enhanced
    environment:
      - REDIS_MCP_SERVER_URL=http://redis-mcp-server:8090
      - CLICKUP_API_TOKEN=${CLICKUP_API_TOKEN}
      - KAFKA_BROKER=kafka:9092
    depends_on:
      redis-mcp-server:
        condition: service_healthy
    networks:
      - go-coffee-network

  # Kafka for event streaming
  kafka:
    image: confluentinc/cp-kafka:latest
    container_name: go-coffee-kafka
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092,PLAINTEXT_INTERNAL://kafka:29092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_INTERNAL:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT_INTERNAL
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: true
    depends_on:
      - zookeeper
    networks:
      - go-coffee-network

  # Zookeeper for Kafka
  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    container_name: go-coffee-zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - go-coffee-network

  # PostgreSQL for persistent data
  postgres:
    image: postgres:15-alpine
    container_name: go-coffee-postgres
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: go_coffee
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - go-coffee-network

  # Prometheus for metrics
  prometheus:
    image: prom/prometheus:latest
    container_name: go-coffee-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    networks:
      - go-coffee-network

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    container_name: go-coffee-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - go-coffee-network

  # Redis Insight for Redis management
  redis-insight:
    image: redislabs/redisinsight:latest
    container_name: go-coffee-redis-insight
    ports:
      - "8001:8001"
    volumes:
      - redis_insight_data:/db
    networks:
      - go-coffee-network

volumes:
  redis_data:
  ollama_data:
  postgres_data:
  prometheus_data:
  grafana_data:
  redis_insight_data:

networks:
  go-coffee-network:
    driver: bridge
